<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 左侧边栏 -->
      <el-aside class="app-aside" width="250px">
        <div class="sidebar-header">
          <h1 class="app-title">
            <el-icon><DataAnalysis /></el-icon>
            AI SQL PCAP
          </h1>
        </div>
        <el-menu
          :default-active="$route.path"
          mode="vertical"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          @select="handleMenuSelect"
          class="sidebar-menu"
        >
          <el-menu-item index="/">
            <el-icon><DataBoard /></el-icon>
            <span>MySQL抓包</span>
          </el-menu-item>
          <el-menu-item index="/mongo">
            <el-icon><Document /></el-icon>
            <span>MongoDB抓包</span>
          </el-menu-item>
          <el-menu-item index="/postgres">
            <el-icon><Monitor /></el-icon>
            <span>PostgreSQL抓包</span>
          </el-menu-item>
          <el-menu-item index="/gaussdb">
            <el-icon><DataLine /></el-icon>
            <span>GaussDB抓包</span>
          </el-menu-item>
          <el-menu-item index="/oracle">
            <el-icon><Coin /></el-icon>
            <span>Oracle抓包</span>
          </el-menu-item>
          <el-menu-item index="/downloads">
            <el-icon><Download /></el-icon>
            <span>下载PCAP包</span>
          </el-menu-item>
          <el-menu-item index="/databases">
            <el-icon><Setting /></el-icon>
            <span>数据库管理</span>
          </el-menu-item>
          <el-menu-item index="/servers">
            <el-icon><Connection /></el-icon>
            <span>服务器管理</span>
          </el-menu-item>
          <el-menu-item index="/tasks">
            <el-icon><Timer /></el-icon>
            <span>任务管理</span>
          </el-menu-item>
          <el-menu-item index="/test-cases">
            <el-icon><List /></el-icon>
            <span>测试用例管理</span>
          </el-menu-item>
          <el-menu-item index="/step-validation">
            <el-icon><DataAnalysis /></el-icon>
            <span>步骤校验</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 右侧内容区域 -->
      <el-container>
        <el-header class="app-header">
          <div class="header-content">
            <div class="header-actions">
              <el-badge :value="captureStatus.is_capturing ? '捕获中' : ''" type="success">
                <el-button
                  :type="captureStatus.is_capturing ? 'danger' : 'primary'"
                  :icon="captureStatus.is_capturing ? 'VideoPause' : 'VideoPlay'"
                  @click="toggleCapture"
                  :loading="captureLoading"
                >
                  {{ captureStatus.is_capturing ? '停止捕获' : '开始捕获' }}
                </el-button>
              </el-badge>
              <el-button type="info" icon="Refresh" @click="checkStatus" :loading="statusLoading">
                刷新状态
              </el-button>
            </div>
          </div>
        </el-header>

        <el-main class="app-main">
          <router-view />
        </el-main>

        <el-footer class="app-footer">
          <div class="footer-content">
            <span>AI SQL PCAP Analyzer v1.0.0</span>
            <span>MySQL: {{ connectionStatus ? '已连接' : '未连接' }}</span>
            <span>抓包状态: {{ captureStatus.is_capturing ? '进行中' : '停止' }}</span>
          </div>
        </el-footer>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { DataAnalysis, DataBoard, Document, Monitor, DataLine, Coin, Setting, Download, Connection, Timer, List } from '@element-plus/icons-vue'
import apiService from './services/api'

export default {
  name: 'App',
  components: {
    DataAnalysis,
    DataBoard,
    Document,
    Monitor,
    DataLine,
    Coin,
    Setting,
    Download,
    Connection,
    Timer,
    List
  },
  setup() {
    const router = useRouter()
    const captureStatus = ref({
      is_capturing: false,
      current_file: null
    })
    const connectionStatus = ref(false)
    const captureLoading = ref(false)
    const statusLoading = ref(false)

    const checkStatus = async () => {
      statusLoading.value = true
      try {
        // 移除健康检查调用，只检查抓包状态
        // const healthData = await apiService.getHealth()
        // connectionStatus.value = healthData.mysql_connection
        connectionStatus.value = true // 默认显示已连接

        const captureData = await apiService.getCaptureStatus()
        captureStatus.value = captureData
      } catch (error) {
        ElMessage.error('获取抓包状态失败: ' + error.message)
      } finally {
        statusLoading.value = false
      }
    }

    const toggleCapture = async () => {
      captureLoading.value = true
      try {
        if (captureStatus.value.is_capturing) {
          await apiService.stopCapture()
          ElMessage.success('数据包捕获已停止')
        } else {
          const result = await apiService.startCapture()
          ElMessage.success('数据包捕获已启动: ' + result.packet_file)
        }
        await checkStatus()
      } catch (error) {
        ElMessage.error('操作失败: ' + error.message)
      } finally {
        captureLoading.value = false
      }
    }

    const handleMenuSelect = (index) => {
      router.push(index)
    }

    onMounted(() => {
      // 移除自动健康检查，只在用户手动点击时检查
      // checkStatus()
      // setInterval(checkStatus, 5000)
    })

    return {
      captureStatus,
      connectionStatus,
      captureLoading,
      statusLoading,
      checkStatus,
      toggleCapture,
      handleMenuSelect
    }
  }
}
</script>

<style lang="scss">
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  height: 100vh;
}

.app-container {
  height: 100vh;
}

.app-aside {
  background-color: #304156;

  .sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #434a50;

    .app-title {
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #bfcbd9;
    }
  }

  .sidebar-menu {
    border-right: none;

    .el-menu-item {
      height: 50px;
      line-height: 50px;

      &:hover {
        background-color: #263445 !important;
      }

      &.is-active {
        background-color: #409EFF !important;
        color: #fff !important;

        .el-icon {
          color: #fff !important;
        }
      }

      .el-icon {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0 20px;
  height: 60px;

  .header-content {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;

    .header-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
}

.app-main {
  padding: 20px;
  background-color: #f5f7fa;
}

.app-footer {
  background-color: #303133;
  color: #909399;
  padding: 0 20px;
  
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    font-size: 14px;
  }
}

// 全局样式
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-button {
  border-radius: 6px;
}

.el-input {
  .el-input__wrapper {
    border-radius: 6px;
  }
}
</style>
