<template>
  <div class="test-case-management">
    <div class="header">
      <h2>测试用例管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建用例
        </el-button>
        <el-button type="success" @click="showAIDialog = true">
          <el-icon><MagicStick /></el-icon>
          AI生成用例
        </el-button>
        <el-button type="warning" @click="standardizeFormat" :loading="standardizing">
          <el-icon><Tools /></el-icon>
          标准化格式
        </el-button>
        <el-button type="warning" @click="showBatchExecutionDialog = true" :disabled="selectedTestCases.length === 0">
          <el-icon><VideoPlay /></el-icon>
          批量执行 ({{ selectedTestCases.length }})
        </el-button>
        <el-button @click="showStatistics = true">
          <el-icon><DataAnalysis /></el-icon>
          统计信息
        </el-button>
        <el-dropdown @command="handleExport" trigger="click">
          <el-button type="info">
            <el-icon><Download /></el-icon>
            导出用例
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="excel">导出为Excel</el-dropdown-item>
              <el-dropdown-item command="csv">导出为CSV</el-dropdown-item>
              <el-dropdown-item command="json">导出为JSON</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-button type="info" @click="showBatchReportDialog = true">
          <el-icon><Download /></el-icon>
          批次报告
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索标题、前置条件、预期结果..."
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.module" placeholder="选择模块" clearable>
            <el-option
              v-for="module in modules"
              :key="module"
              :label="module"
              :value="module"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.priority" placeholder="优先级" clearable>
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="critical" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.status" placeholder="状态" clearable>
            <el-option label="草稿" value="draft" />
            <el-option label="激活" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
            <el-option label="已废弃" value="deprecated" />
            <el-option label="已归档" value="archived" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.author" placeholder="创建者" clearable>
            <el-option
              v-for="author in authors"
              :key="author"
              :label="author"
              :value="author"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.database_type" placeholder="数据库类型" clearable>
            <el-option label="MySQL" value="mysql" />
            <el-option label="PostgreSQL" value="postgresql" />
            <el-option label="MongoDB" value="mongodb" />
            <el-option label="Oracle" value="oracle" />
            <el-option label="GaussDB" value="gaussdb" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="searchForm.database_version"
            placeholder="数据库版本"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 10px;">
        <el-col :span="12">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            type="danger"
            :disabled="selectedTestCases.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除 ({{ selectedTestCases.length }})
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 测试用例列表 -->
    <div class="test-case-list">
      <el-table
        :data="testCases"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="module" label="模块" width="120" />
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="scope">
            <el-tag :type="getPriorityType(scope.row.priority)">
              {{ getPriorityText(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="author" label="创建者" width="100" />
        <el-table-column prop="database_type" label="数据库类型" width="100">
          <template #default="scope">
            <el-tag :type="getDatabaseTypeColor(scope.row.database_type)" size="small">
              {{ getDatabaseTypeName(scope.row.database_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="database_version" label="数据库版本" width="120">
          <template #default="scope">
            <span v-if="scope.row.database_version">{{ scope.row.database_version }}</span>
            <span v-else class="text-gray-400">未指定</span>
          </template>
        </el-table-column>
        <el-table-column prop="execution_count" label="执行次数" width="80" />
        <el-table-column prop="success_count" label="成功次数" width="80" />
        <el-table-column label="通过率" width="80">
          <template #default="scope">
            {{ getPassRate(scope.row) }}%
          </template>
        </el-table-column>

        <el-table-column prop="updated_at" label="更新时间" width="150">
          <template #default="scope">
            {{ formatDate(scope.row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <!-- 第一行：主要操作 -->
              <div class="action-row">
                <el-button size="small" plain @click="viewTestCase(scope.row)">
                  <el-icon><Document /></el-icon>
                  查看
                </el-button>
                <el-button size="small" plain @click="editTestCase(scope.row)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button
                  size="small"
                  plain
                  @click="automatedExecute(scope.row)"
                  :disabled="!canAutoExecute(scope.row)"
                >
                  <el-icon><VideoPlay /></el-icon>
                  执行
                </el-button>
              </div>

              <!-- 第二行：次要操作 -->
              <div class="action-row">
                <el-button
                  size="small"
                  plain
                  @click="viewExecutionRecord(scope.row)"
                  :disabled="!scope.row.last_execution_time"
                >
                  <el-icon><DataAnalysis /></el-icon>
                  执行记录
                </el-button>
                <el-button size="small" plain @click="duplicateTestCase(scope.row)">
                  <el-icon><CopyDocument /></el-icon>
                  复制
                </el-button>
                <el-button size="small" plain @click="deleteTestCase(scope.row)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedTestCases.length > 0">
        <span>已选择 {{ selectedTestCases.length }} 项</span>
        <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <TestCaseDialog
      v-model:visible="showCreateDialog"
      :test-case="currentTestCase"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />

    <!-- 查看详情对话框 -->
    <TestCaseDetailDialog
      v-model:visible="showDetailDialog"
      :test-case="currentTestCase"
    />



    <!-- 自动化执行对话框 -->
    <AutomatedExecutionDialog
      v-model:visible="showAutomatedExecutionDialog"
      :test-case="currentTestCase"
      @success="handleAutomatedExecutionSuccess"
    />

    <!-- 统计信息对话框 -->
    <TestCaseStatisticsDialog
      v-model:visible="showStatistics"
    />

    <!-- AI生成测试用例对话框 -->
    <AITestCaseDialog
      v-model:visible="showAIDialog"
      @success="handleDialogSuccess"
    />

    <!-- 执行记录对话框 -->
    <ExecutionRecordDialog
      v-model:visible="showExecutionRecordDialog"
      :execution-record="currentExecutionRecord"
    />

    <!-- 批量执行对话框 -->
    <BatchExecutionDialog
      v-model:visible="showBatchExecutionDialog"
      :selected-test-cases="selectedTestCases"
      @success="handleBatchExecutionSuccess"
    />

    <!-- 批次报告导出对话框 -->
    <el-dialog v-model="showBatchReportDialog" title="导出批次执行报告" width="720px">
      <el-form :model="batchReportForm" label-width="100px">
        <el-form-item label="批次ID">
          <el-input v-model="batchReportForm.batch_id" placeholder="请输入批量执行的批次ID" />
        </el-form-item>
        <el-form-item label="导出格式">
          <el-select v-model="batchReportForm.format" style="width: 180px">
            <el-option label="Excel" value="excel" />
            <el-option label="CSV" value="csv" />
            <el-option label="JSON" value="json" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="loadBatchDetails" :loading="batchDetailsLoading">加载批次详情</el-button>
        </el-form-item>
        <div v-if="batchDetails" class="batch-meta">
          <div>批次名称：{{ batchDetails.name || batchDetails.batch_id }}</div>
          <div>开始时间：{{ batchDetails.start_time ? formatDateTime(batchDetails.start_time) : '-' }}</div>
          <div>结束时间：{{ batchDetails.end_time ? formatDateTime(batchDetails.end_time) : '-' }}</div>
        </div>
        <el-table v-if="batchDetails && batchDetails.test_case_items" :data="batchDetails.test_case_items" size="small" style="width: 100%; margin-top: 8px;">
          <el-table-column prop="test_case_title" label="用例标题" min-width="200" show-overflow-tooltip />
          <el-table-column label="结果" width="90">
            <template #default="scope">
              <el-tag :type="scope.row.success ? 'success' : 'danger'">{{ scope.row.success ? 'pass' : 'fail' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" width="180">
            <template #default="scope">{{ scope.row.start_time ? formatDateTime(scope.row.start_time) : '-' }}</template>
          </el-table-column>
          <el-table-column label="结束时间" width="180">
            <template #default="scope">{{ scope.row.end_time ? formatDateTime(scope.row.end_time) : '-' }}</template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showBatchReportDialog = false">取 消</el-button>
          <el-button type="primary" @click="downloadBatchReport">导 出</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, DataAnalysis, ArrowDown, MagicStick, VideoPlay, Document, Edit, CopyDocument, Delete, Download, Tools } from '@element-plus/icons-vue'
import TestCaseDialog from '../components/TestCaseDialog.vue'
import TestCaseDetailDialog from '../components/TestCaseDetailDialog.vue'
import AutomatedExecutionDialog from '../components/AutomatedExecutionDialog.vue'
import TestCaseStatisticsDialog from '../components/TestCaseStatisticsDialog.vue'
import AITestCaseDialog from '../components/AITestCaseDialog.vue'
import ExecutionRecordDialog from '../components/ExecutionRecordDialog.vue'
import BatchExecutionDialog from '../components/BatchExecutionDialog.vue'
import testCaseApi from '../services/testCaseApi'
import apiService from '../services/api'

// 响应式数据
const loading = ref(false)
const testCases = ref([])
const selectedTestCases = ref([])
const modules = ref([])
const authors = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框状态
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showAutomatedExecutionDialog = ref(false)
const showStatistics = ref(false)
const showAIDialog = ref(false)
const showExecutionRecordDialog = ref(false)
const showBatchExecutionDialog = ref(false)
const showBatchReportDialog = ref(false)
const batchReportForm = reactive({ batch_id: '', format: 'excel' })
const batchDetailsLoading = ref(false)
const batchDetails = ref(null)
const currentExecutionRecord = ref(null)
const currentTestCase = ref(null)
const isEdit = ref(false)
const standardizing = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  module: '',
  priority: '',
  status: '',
  author: '',
  database_type: '',
  database_version: ''
})

// 生命周期
onMounted(() => {
  loadTestCases()
  loadModules()
  loadAuthors()
})

// 方法
const loadTestCases = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...searchForm
    }
    const response = await testCaseApi.getTestCases(params)
    testCases.value = response.items
    total.value = response.total
  } catch (error) {
    ElMessage.error('加载测试用例失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadModules = async () => {
  try {
    const response = await testCaseApi.getModules()
    modules.value = response.modules
  } catch (error) {
    console.error('加载模块列表失败:', error)
  }
}

const loadAuthors = async () => {
  try {
    const response = await testCaseApi.getAuthors()
    authors.value = response.authors
  } catch (error) {
    console.error('加载创建者列表失败:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadTestCases()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

// 标准化测试用例格式
const standardizeFormat = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将标准化所有测试用例的格式，将SQL语句统一存储到test_data字段中。这将有助于统一数据结构，便于维护和SQL提取。确定要继续吗？',
      '标准化测试用例格式',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    standardizing.value = true
    const response = await testCaseApi.standardizeFormat()

    if (response.success) {
      ElMessage.success(response.message)
      // 刷新测试用例列表
      await loadTestCases()
    } else {
      ElMessage.error(response.message || '标准化失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('标准化失败: ' + error.message)
    }
  } finally {
    standardizing.value = false
  }
}

const handleExport = async (format) => {
  try {
    ElMessage.info('正在导出，请稍候...')

    // 使用当前的筛选条件导出
    const exportParams = {
      ...searchForm
    }

    const response = await testCaseApi.exportTestCases(exportParams, format)

    // 创建下载链接
    const blob = new Blob([response.data], {
      type: response.headers['content-type'] || 'application/octet-stream'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 从响应头获取文件名，或使用默认文件名
    const contentDisposition = response.headers['content-disposition']
    let filename = `test_cases_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename=(.+)/)
      if (filenameMatch) {
        filename = filenameMatch[1].replace(/"/g, '')
      }
    } else {
      // 根据格式设置扩展名
      const extensions = { excel: 'xlsx', csv: 'csv', json: 'json' }
      filename += `.${extensions[format] || 'xlsx'}`
    }

    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  }
}

// 批量执行报告下载
const downloadBatchReport = async () => {
  if (!batchReportForm.batch_id) {
    ElMessage.warning('请输入批次ID')
    return
  }
  try {
    ElMessage.info('正在导出批次报告，请稍候...')
    const response = await testCaseApi.exportBatchReport(batchReportForm.batch_id, batchReportForm.format)
    const blob = new Blob([response.data], {
      type: response.headers['content-type'] || 'application/octet-stream'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    const ts = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const ext = batchReportForm.format === 'excel' ? 'xlsx' : (batchReportForm.format === 'csv' ? 'csv' : 'json')
    link.download = `batch_report_${batchReportForm.batch_id}_${ts}.${ext}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('批次报告导出成功')
    showBatchReportDialog.value = false
  } catch (error) {
    console.error('批次报告导出失败:', error)
    ElMessage.error('批次报告导出失败: ' + error.message)
  }
}

// 加载批次详情（显示执行用例列表与时间）
const loadBatchDetails = async () => {
  if (!batchReportForm.batch_id) {
    ElMessage.warning('请输入批次ID')
    return
  }
  try {
    batchDetailsLoading.value = true
    const res = await apiService.get(`/test-case-execution/batch-status/${batchReportForm.batch_id}`)
    batchDetails.value = res || null
  } catch (e) {
    console.error('加载批次详情失败:', e)
    batchDetails.value = null
    ElMessage.error('加载批次详情失败: ' + e.message)
  } finally {
    batchDetailsLoading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedTestCases.value = selection
}

const handleBatchDelete = async () => {
  if (selectedTestCases.value.length === 0) {
    ElMessage.warning('请选择要删除的测试用例')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedTestCases.value.length} 个测试用例吗？此操作不可恢复。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const testCaseIds = selectedTestCases.value.map(item => item.id)
    const response = await testCaseApi.batchDeleteTestCases(testCaseIds)

    ElMessage.success(response.message || `成功删除 ${response.deleted_count} 个测试用例`)

    // 清空选择
    selectedTestCases.value = []

    // 重新加载数据
    loadTestCases()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败: ' + error.message)
    }
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadTestCases()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadTestCases()
}

const viewTestCase = (testCase) => {
  currentTestCase.value = testCase
  showDetailDialog.value = true
}

const editTestCase = (testCase) => {
  currentTestCase.value = testCase
  isEdit.value = true
  showCreateDialog.value = true
}



const automatedExecute = (testCase) => {
  currentTestCase.value = testCase
  showAutomatedExecutionDialog.value = true
}

const canAutoExecute = (testCase) => {
  // 检查测试用例是否可以自动执行
  // 需要有test_steps且数据库类型支持
  const supportedDatabases = ['mysql', 'postgresql', 'mongodb', 'oracle', 'gaussdb']
  return testCase.test_steps &&
         testCase.test_steps.length > 0 &&
         supportedDatabases.includes(testCase.database_type)
}

const duplicateTestCase = async (testCase) => {
  try {
    const { value: newTitle } = await ElMessageBox.prompt('请输入新用例标题', '复制测试用例', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValue: testCase.title + ' - 副本'
    })
    
    await testCaseApi.duplicateTestCase(testCase.id, newTitle)
    ElMessage.success('复制成功')
    loadTestCases()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('复制失败: ' + error.message)
    }
  }
}

const deleteTestCase = async (testCase) => {
  try {
    await ElMessageBox.confirm(`确定要删除测试用例"${testCase.title}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await testCaseApi.deleteTestCase(testCase.id)
    ElMessage.success('删除成功')
    loadTestCases()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}



const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedTestCases.value.length} 个测试用例吗？`, '确认删除', {
      type: 'warning'
    })
    
    for (const testCase of selectedTestCases.value) {
      await testCaseApi.deleteTestCase(testCase.id)
    }
    
    ElMessage.success('批量删除成功')
    loadTestCases()
    selectedTestCases.value = []
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败: ' + error.message)
    }
  }
}

const handleDialogSuccess = () => {
  showCreateDialog.value = false
  currentTestCase.value = null
  isEdit.value = false
  loadTestCases()
}



const handleAutomatedExecutionSuccess = () => {
  showAutomatedExecutionDialog.value = false
  currentTestCase.value = null
  loadTestCases()
}

const handleBatchExecutionSuccess = () => {
  showBatchExecutionDialog.value = false
  selectedTestCases.value = []
  ElMessage.success('批量执行任务已提交，请在任务管理页面查看执行进度')
}

// 工具方法
const getPriorityType = (priority) => {
  const types = {
    low: '',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return types[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return texts[priority] || priority
}

const getStatusType = (status) => {
  const types = {
    draft: 'info',
    active: 'success',
    completed: 'primary',
    failed: 'danger',
    deprecated: 'warning',
    archived: ''
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    active: '激活',
    completed: '已完成',
    failed: '失败',
    deprecated: '已废弃',
    archived: '已归档'
  }
  return texts[status] || status
}

const getPassRate = (testCase) => {
  if (testCase.execution_count === 0) return 0
  return Math.round((testCase.success_count / testCase.execution_count) * 100)
}

// 统一的时区格式化函数，强制使用+8时区
const formatDateTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  // 使用时区工具函数，强制使用+8时区
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 获取执行结果类型（用于标签颜色）
const getExecutionResultType = (result) => {
  switch (result) {
    case 'pass': return 'success'
    case 'fail': return 'danger'
    case 'blocked': return 'warning'
    case 'skip': return 'info'
    default: return 'info'
  }
}

// 获取执行结果文本
const getExecutionResultText = (result) => {
  switch (result) {
    case 'pass': return '通过'
    case 'fail': return '失败'
    case 'blocked': return '阻塞'
    case 'skip': return '跳过'
    default: return '未知'
  }
}

// 查看执行记录
const viewExecutionRecord = async (testCase) => {
  try {
    const response = await testCaseApi.getLastExecution(testCase.id)
    // 检查响应数据，可能直接是数据对象，也可能在data字段中
    const lastExecution = response.data || response

    console.log('执行记录响应:', response)
    console.log('解析后的执行记录:', lastExecution)

    if (!lastExecution || !lastExecution.execution_time) {
      ElMessage.warning('该测试用例暂无执行记录')
      return
    }

    // 显示执行记录对话框，使用与自动化执行对话框相同的样式
    showExecutionRecordDialog.value = true
    currentExecutionRecord.value = {
      testCase: testCase,
      executionData: lastExecution
    }
  } catch (error) {
    console.error('获取执行记录失败:', error)
    ElMessage.error('获取执行记录失败')
  }
}

const getDatabaseTypeName = (type) => {
  const nameMap = {
    'mysql': 'MySQL',
    'postgresql': 'PostgreSQL',
    'mongodb': 'MongoDB',
    'oracle': 'Oracle',
    'gaussdb': 'GaussDB'
  }
  return nameMap[type] || type
}

const getDatabaseTypeColor = (type) => {
  const colorMap = {
    'mysql': 'primary',
    'postgresql': 'success',
    'mongodb': 'warning',
    'oracle': 'danger',
    'gaussdb': 'info'
  }
  return colorMap[type] || 'info'
}
</script>

<style scoped>
.test-case-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-filters {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.test-case-list {
  background: white;
  border-radius: 4px;
  padding: 20px;
}

.batch-actions {
  margin: 20px 0;
  padding: 10px;
  background: #f0f9ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 最近执行记录样式 */
.last-execution {
  font-size: 12px;
}

.execution-result {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.execution-time {
  color: #666;
  font-size: 11px;
}

.capture-files {
  margin-top: 4px;
}

.capture-link {
  font-size: 11px;
  padding: 0;
  height: auto;
  color: #409EFF;
}

.no-execution {
  color: #999;
  font-size: 12px;
}

/* 操作列样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.action-row {
  display: flex;
  gap: 4px;
  justify-content: flex-start;
  flex-wrap: nowrap;
}

.action-row .el-button {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  min-width: auto;
  height: 24px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  color: #606266;
  transition: all 0.2s ease;
}

.action-row .el-button:hover:not(:disabled) {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
  color: #409eff;
}

.action-row .el-button .el-icon {
  margin-right: 2px;
  font-size: 12px;
}

.action-row .el-button span {
  font-size: 11px;
}

/* 按钮状态样式 */
.action-row .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

.action-row .el-button:disabled:hover {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}
</style>
