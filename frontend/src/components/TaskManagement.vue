<template>
  <div class="task-management">
    <div class="header">
      <h2>异步任务管理</h2>
      <div class="header-actions">
        <el-button @click="refreshTasks" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-dropdown @command="handleCleanupCommand" trigger="click">
          <el-button type="warning">
            <el-icon><Delete /></el-icon>
            清理过期任务
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="all">清理所有过期任务</el-dropdown-item>
              <el-dropdown-item divided>按状态清理</el-dropdown-item>
              <el-dropdown-item command="failed">清理失败任务</el-dropdown-item>
              <el-dropdown-item command="cancelled">清理已取消任务</el-dropdown-item>
              <el-dropdown-item divided>按类型清理</el-dropdown-item>
              <el-dropdown-item command="mysql_capture">清理MySQL抓包任务</el-dropdown-item>
              <el-dropdown-item command="postgres_capture">清理PostgreSQL抓包任务</el-dropdown-item>
              <el-dropdown-item command="mongo_capture">清理MongoDB抓包任务</el-dropdown-item>
              <el-dropdown-item command="gaussdb_capture">清理GaussDB抓包任务</el-dropdown-item>
              <el-dropdown-item command="docker_build">清理Docker构建任务</el-dropdown-item>
              <el-dropdown-item divided>按AI类型清理</el-dropdown-item>
              <el-dropdown-item command="ai_mysql_capture">清理AI+MySQL抓包任务</el-dropdown-item>
              <el-dropdown-item command="ai_postgres_capture">清理AI+PostgreSQL抓包任务</el-dropdown-item>
              <el-dropdown-item command="ai_mongo_capture">清理AI+MongoDB抓包任务</el-dropdown-item>
              <el-dropdown-item command="ai_gaussdb_capture">清理AI+GaussDB抓包任务</el-dropdown-item>
              <el-dropdown-item divided>按测试用例类型清理</el-dropdown-item>
              <el-dropdown-item command="batch_test_case_execution">清理批量测试用例执行任务</el-dropdown-item>
              <el-dropdown-item command="single_test_case_execution">清理单个测试用例执行任务</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.total }}</div>
          <div class="stat-label">总任务数</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.running }}</div>
          <div class="stat-label">运行中</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.failed }}</div>
          <div class="stat-label">失败</div>
        </div>
      </el-card>
    </div>

    <!-- 过滤器 -->
    <div class="filters">
      <el-select v-model="filters.taskType" placeholder="任务类型" clearable @change="loadTasks">
        <el-option label="MySQL抓包" value="mysql_capture" />
        <el-option label="PostgreSQL抓包" value="postgres_capture" />
        <el-option label="MongoDB抓包" value="mongo_capture" />
        <el-option label="GaussDB抓包" value="gaussdb_capture" />
        <el-option label="Docker构建" value="docker_build" />
        <el-option label="AI+MySQL抓包" value="ai_mysql_capture" />
        <el-option label="AI+PostgreSQL抓包" value="ai_postgres_capture" />
        <el-option label="AI+MongoDB抓包" value="ai_mongo_capture" />
        <el-option label="AI+GaussDB抓包" value="ai_gaussdb_capture" />
        <el-option label="批量测试用例执行" value="batch_test_case_execution" />
        <el-option label="单个测试用例执行" value="single_test_case_execution" />
      </el-select>
      <el-select v-model="filters.status" placeholder="任务状态" clearable @change="loadTasks">
        <el-option label="等待中" value="pending" />
        <el-option label="运行中" value="running" />
        <el-option label="已完成" value="completed" />
        <el-option label="失败" value="failed" />
        <el-option label="已取消" value="cancelled" />
      </el-select>
    </div>

    <!-- 任务列表 -->
    <el-table :data="tasks" v-loading="loading" stripe>
      <el-table-column prop="task_id" label="任务ID" width="280">
        <template #default="{ row }">
          <el-text class="task-id" size="small">{{ row.task_id }}</el-text>
        </template>
      </el-table-column>
      
      <el-table-column prop="type" label="类型" width="200">
        <template #default="{ row }">
          <el-tag :type="getTaskTypeColor(row && row.type)">
            {{ getTaskTypeName(row && row.type) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="描述" width="200">
        <template #default="{ row }">
          <div v-if="row && row.type === 'batch_test_case_execution'">
            <div class="batch-info">
              <strong>{{ row.batch_name || '批量执行' }}</strong>
              <div class="batch-details">
                <el-tag size="small" type="info">{{ row.database_type }}</el-tag>
                <span class="case-count">{{ row.total_test_cases || 0 }}个用例</span>
              </div>
            </div>
          </div>
          <div v-else-if="row && row.type === 'single_test_case_execution'">
            <div class="single-test-case-info">
              <strong>{{ row.test_case_title || '单个用例执行' }}</strong>
              <div class="test-case-details">
                <el-tag size="small" type="info">{{ row.capture_enabled ? '启用抓包' : '禁用抓包' }}</el-tag>
                <span class="config-id">配置ID: {{ row.config_id }}</span>
              </div>
            </div>
          </div>
          <div v-else-if="row && row.natural_query">
            <el-text size="small" class="natural-query">{{ row.natural_query.substring(0, 50) }}...</el-text>
          </div>
          <div v-else-if="row && row.sql_query">
            <el-text size="small" class="sql-query">{{ row.sql_query.substring(0, 50) }}...</el-text>
          </div>
          <div v-else>
            <el-text size="small" type="info">-</el-text>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusColor(row.status)">
            {{ getStatusName(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="进度">
        <template #default="{ row }">
          <div class="progress-container">
            <el-progress 
              :percentage="row.progress || 0" 
              :status="getProgressStatus(row.status)"
              :stroke-width="8"
            />
            <div class="progress-message" v-if="row.message">
              {{ row.message }}
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="created_at" label="创建时间" width="160">
        <template #default="{ row }">
          {{ formatTime(row.created_at) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="duration" label="耗时" width="100">
        <template #default="{ row }">
          <span v-if="row.duration">{{ formatDuration(row.duration) }}</span>
          <span v-else-if="row.status === 'running'">{{ getRunningDuration(row.created_at) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button 
            size="small" 
            @click="viewTaskDetail(row)"
            type="primary"
            link
          >
            详情
          </el-button>
          <el-button 
            size="small" 
            @click="cancelTask(row.task_id)"
            type="danger"
            link
            v-if="row.status === 'pending' || row.status === 'running'"
          >
            取消
          </el-button>
          <el-button 
            size="small" 
            @click="downloadResult(row)"
            type="success"
            link
            v-if="row.status === 'completed' && row.result && row.result.capture_file"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog v-model="detailDialog.visible" title="任务详情" width="60%">
      <div v-if="detailDialog.task" class="task-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ detailDialog.task && detailDialog.task.task_id }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ getTaskTypeName(detailDialog.task && detailDialog.task.type) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(detailDialog.task && detailDialog.task.status)">
              {{ getStatusName(detailDialog.task && detailDialog.task.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="进度">{{ detailDialog.task && detailDialog.task.progress || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(detailDialog.task && detailDialog.task.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatTime(detailDialog.task && detailDialog.task.updated_at) }}</el-descriptions-item>
        </el-descriptions>

        <!-- AI任务特殊信息 -->
        <div class="detail-section" v-if="detailDialog.task && isAITask(detailDialog.task.type) && detailDialog.task.natural_query">
          <h4>自然语言查询</h4>
          <el-input
            type="textarea"
            :value="detailDialog.task.natural_query"
            readonly
            :rows="3"
          />
        </div>

        <div class="detail-section" v-if="detailDialog.task && (detailDialog.task.sql_query || detailDialog.task.mongo_query)">
          <h4>{{ detailDialog.task && isAITask(detailDialog.task.type) ? '生成的查询语句' : '查询语句' }}</h4>
          <el-input
            type="textarea"
            :value="detailDialog.task.sql_query || detailDialog.task.mongo_query"
            readonly
            :rows="4"
          />
        </div>

        <!-- Docker构建任务特殊信息 -->
        <div class="detail-section" v-if="detailDialog.task && detailDialog.task.type === 'docker_build' && detailDialog.task.build_requests">
          <h4>构建请求 ({{ detailDialog.task.total_requests }}个)</h4>
          <div class="build-requests">
            <div
              v-for="(request, index) in detailDialog.task.build_requests"
              :key="index"
              class="build-request-item"
            >
              <div class="request-header">
                <span class="request-index">请求 {{ index + 1 }}</span>
                <el-tag :type="getTaskTypeColor(request && request.database_type)">
                  {{ request && request.database_type ? request.database_type.toUpperCase() : '' }}
                </el-tag>
              </div>
              <div class="request-content">
                <strong>AI提示词:</strong>
                <p>{{ request && request.ai_prompt }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 批量测试用例执行任务特殊信息 -->
        <div class="detail-section" v-if="detailDialog.task && detailDialog.task.type === 'batch_test_case_execution'">
          <h4>批量执行信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="批量名称">{{ detailDialog.task.batch_name }}</el-descriptions-item>
            <el-descriptions-item label="数据库类型">{{ detailDialog.task.database_type }}</el-descriptions-item>
            <el-descriptions-item label="数据库版本">{{ detailDialog.task.database_version }}</el-descriptions-item>
            <el-descriptions-item label="总用例数">{{ detailDialog.task.total_test_cases }}</el-descriptions-item>
            <el-descriptions-item label="抓包启用">
              <el-tag :type="detailDialog.task.capture_enabled ? 'success' : 'info'">
                {{ detailDialog.task.capture_enabled ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="遇到失败停止">
              <el-tag :type="detailDialog.task.stop_on_failure ? 'warning' : 'info'">
                {{ detailDialog.task.stop_on_failure ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>

          <div v-if="detailDialog.task.description" style="margin-top: 10px;">
            <strong>描述:</strong>
            <p>{{ detailDialog.task.description }}</p>
          </div>

          <!-- 批量执行结果详情 -->
          <div v-if="detailDialog.task.result && detailDialog.task.result.execution_results" style="margin-top: 15px;">
            <h5>执行结果详情</h5>
            <el-table :data="detailDialog.task.result.execution_results" border size="small" max-height="300">
              <el-table-column prop="test_case_title" label="测试用例" width="200" show-overflow-tooltip />
              <el-table-column label="状态" width="100">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.success ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ scope.row.success ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="duration" label="耗时(秒)" width="100" />
              <el-table-column prop="error_message" label="错误信息" show-overflow-tooltip />
              <el-table-column label="抓包文件" width="120">
                <template #default="scope">
                  <span v-if="scope.row.capture_files && scope.row.capture_files.length > 0">
                    {{ scope.row.capture_files.length }} 个文件
                  </span>
                  <span v-else>无</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div class="detail-section" v-if="detailDialog.task.result">
          <h4>执行结果</h4>

          <!-- Docker构建结果特殊显示 -->
          <div v-if="detailDialog.task && detailDialog.task.type === 'docker_build'" class="docker-build-result">
            <el-alert
              :type="detailDialog.task.result && detailDialog.task.result.successful_builds > 0 ? 'success' : 'error'"
              :title="detailDialog.task.result && detailDialog.task.result.summary"
              show-icon
            />
            
            <el-descriptions :column="3" border style="margin-top: 15px;">
              <el-descriptions-item label="总请求数">{{ detailDialog.task.result && detailDialog.task.result.total_requests }}</el-descriptions-item>
              <el-descriptions-item label="成功构建">
                <el-tag type="success">{{ detailDialog.task.result && detailDialog.task.result.successful_builds }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="失败构建">
                <el-tag type="danger">{{ detailDialog.task.result && detailDialog.task.result.failed_builds }}</el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <!-- 构建结果详情 -->
            <div v-if="detailDialog.task.result && detailDialog.task.result.results" class="build-results" style="margin-top: 15px;">
              <h5>构建结果详情</h5>
              <div
                v-for="(buildResult, index) in detailDialog.task.result.results"
                :key="index"
                class="build-result-item"
              >
                <div class="result-header">
                  <span class="result-index">构建 {{ index + 1 }}</span>
                  <el-tag :type="buildResult.success ? 'success' : 'danger'">
                    {{ buildResult.success ? '成功' : '失败' }}
                  </el-tag>
                </div>
                
                <div v-if="buildResult.success && buildResult.result" class="result-details">
                  <el-descriptions :column="2" border size="small">
                    <el-descriptions-item label="容器名称">{{ buildResult.result.container_name }}</el-descriptions-item>
                    <el-descriptions-item label="端口">{{ buildResult.result.port }}</el-descriptions-item>
                    <el-descriptions-item label="用户名">{{ buildResult.result.username }}</el-descriptions-item>
                    <el-descriptions-item label="密码">{{ buildResult.result.password }}</el-descriptions-item>
                    <el-descriptions-item label="数据库名">{{ buildResult.result.database_name }}</el-descriptions-item>
                    <el-descriptions-item label="配置ID" v-if="buildResult.result.database_config_id">
                      {{ buildResult.result.database_config_id }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                
                <div v-else-if="buildResult.error" class="error-details">
                  <el-alert
                    :title="buildResult.result && buildResult.result.message || '构建失败'"
                    type="error"
                    show-icon
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 其他任务类型的结果显示 -->
          <el-input
            v-else
            type="textarea"
            :value="JSON.stringify(detailDialog.task.result, null, 2)"
            readonly
            :rows="8"
          />
        </div>

        <div class="detail-section" v-if="detailDialog.task.error">
          <h4>错误信息</h4>
          <el-alert :title="detailDialog.task.error" type="error" show-icon />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Delete, ArrowDown } from '@element-plus/icons-vue'
import apiService from '@/services/api'

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const stats = reactive({
  total: 0,
  pending: 0,
  running: 0,
  completed: 0,
  failed: 0,
  cancelled: 0
})

const filters = reactive({
  taskType: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const detailDialog = reactive({
  visible: false,
  task: null
})

let refreshTimer = null

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    const response = await apiService.getTasks(
      pagination.page,
      pagination.pageSize,
      filters.taskType || null
    )

    tasks.value = response.tasks
    pagination.total = response.total

  } catch (error) {
    console.error('加载任务列表失败:', error)
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await apiService.getTaskStats()
    Object.assign(stats, response)
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const refreshTasks = async () => {
  await Promise.all([loadTasks(), loadStats()])
}

const cancelTask = async (taskId) => {
  try {
    await ElMessageBox.confirm('确定要取消这个任务吗？', '确认取消', {
      type: 'warning'
    })
    
    await apiService.cancelTask(taskId)
    ElMessage.success('任务已取消')
    await refreshTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消任务失败:', error)
      ElMessage.error('取消任务失败')
    }
  }
}

const cleanupTasks = async (taskType = null, status = null) => {
  try {
    // 构建确认消息
    let confirmMessage = '确定要清理过期的'
    const messageParts = []

    if (taskType) {
      messageParts.push(getTaskTypeName(taskType))
    }
    if (status) {
      const statusNames = {
        'failed': '失败',
        'cancelled': '已取消'
      }
      messageParts.push(statusNames[status] || status)
    }

    if (messageParts.length > 0) {
      confirmMessage += messageParts.join(' ') + '任务吗？'
    } else {
      confirmMessage += '所有任务吗？'
    }

    await ElMessageBox.confirm(confirmMessage, '确认清理', {
      type: 'warning'
    })

    await apiService.cleanupTasks(taskType, status)
    ElMessage.success('清理任务已启动')
    setTimeout(refreshTasks, 2000) // 2秒后刷新
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理任务失败:', error)
      ElMessage.error('清理任务失败')
    }
  }
}

const handleCleanupCommand = (command) => {
  if (command === 'all') {
    cleanupTasks(null, null)
  } else if (command === 'failed' || command === 'cancelled') {
    cleanupTasks(null, command)
  } else {
    cleanupTasks(command, null)
  }
}

const viewTaskDetail = async (task) => {
  try {
    const response = await apiService.getTask(task.task_id)
    detailDialog.task = response
    detailDialog.visible = true
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')
  }
}

const downloadResult = async (task) => {
  if (task.result && task.result.capture_file) {
    const filename = task.result.capture_file.split('/').pop()
    try {
      const response = await fetch(`/api/captures/download/${filename}`)
      if (!response.ok) throw new Error('下载失败')

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      ElMessage.success('文件下载成功')
    } catch (error) {
      ElMessage.error('下载文件失败: ' + error.message)
    }
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  loadTasks()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadTasks()
}

// 辅助方法
const getTaskTypeName = (type) => {
  const names = {
    mysql_capture: 'MySQL抓包',
    postgres_capture: 'PostgreSQL抓包',
    mongo_capture: 'MongoDB抓包',
    gaussdb_capture: 'GaussDB抓包',
    docker_build: 'Docker构建',
    ai_mysql_capture: 'AI+MySQL抓包',
    ai_postgres_capture: 'AI+PostgreSQL抓包',
    ai_mongo_capture: 'AI+MongoDB抓包',
    ai_gaussdb_capture: 'AI+GaussDB抓包',
    batch_test_case_execution: '批量测试用例执行',
    single_test_case_execution: '单个测试用例执行'
  }
  return names[type] || type
}

const isAITask = (type) => {
  return type && type.startsWith('ai_')
}

const getTaskTypeColor = (type) => {
  const colors = {
    mysql_capture: 'primary',
    postgres_capture: 'success',
    mongo_capture: 'warning',
    docker_build: 'danger',
    ai_mysql_capture: 'info',
    ai_postgres_capture: 'success',
    ai_mongo_capture: 'warning',
    batch_test_case_execution: 'primary',
    single_test_case_execution: 'info'
  }
  return colors[type] || 'info'
}

const getStatusName = (status) => {
  const names = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return names[status] || status
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'info',
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    cancelled: 'warning'
  }
  return colors[status] || 'info'
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return null
}

// 导入时区工具
import { formatTime as formatTimeWithTimezone, formatDuration as formatDurationUtil, getRunningDuration as getRunningDurationUtil } from '@/utils/timezone'

const formatTime = (timeStr) => {
  return formatTimeWithTimezone(timeStr)
}

const formatDuration = (seconds) => {
  return formatDurationUtil(seconds)
}

const getRunningDuration = (startTime) => {
  return getRunningDurationUtil(startTime)
}

// 生命周期
onMounted(() => {
  refreshTasks()
  // 每10秒自动刷新
  refreshTimer = setInterval(refreshTasks, 10000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.task-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.task-id {
  font-family: monospace;
  font-size: 12px;
}

.progress-container {
  width: 100%;
}

.progress-message {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
  text-align: center;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.task-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

/* Docker构建任务样式 */
.build-requests {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.build-request-item {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 15px;
  background-color: #fafafa;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.request-index {
  font-weight: bold;
  color: #409eff;
}

.request-content p {
  margin: 5px 0;
  color: #606266;
}

.docker-build-result {
  max-height: 500px;
  overflow-y: auto;
}

.build-results {
  max-height: 300px;
  overflow-y: auto;
}

.build-result-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 10px;
  background-color: #fafbfc;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
}

.result-index {
  font-weight: bold;
  color: #409eff;
}

.success-details,
.error-details {
  margin-top: 10px;
}

/* 批量执行任务样式 */
.batch-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.batch-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.case-count {
  font-size: 12px;
  color: #909399;
}

.natural-query,
.sql-query {
  color: #606266;
  font-family: 'Courier New', monospace;
}

/* 单个测试用例执行任务样式 */
.single-test-case-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.test-case-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-id {
  font-size: 12px;
  color: #909399;
}
</style>
