#!/usr/bin/env python3
"""
ARQ监控工具集合
提供多种ARQ监控和调试功能的统一入口
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 80)
    print("🔍 ARQ监控工具集合")
    print("=" * 80)
    print("提供完整的ARQ任务监控、日志查看和调试功能")
    print()

def run_log_viewer(args):
    """运行日志查看器"""
    script_path = Path(__file__).parent / "utils" / "arq_log_viewer.py"
    cmd = [sys.executable, str(script_path)]
    
    if args.log_type:
        cmd.extend(['--type', args.log_type])
    if args.tail:
        cmd.extend(['--tail', str(args.tail)])
    if args.follow:
        cmd.append('--follow')
    if args.search:
        cmd.extend(['--search', args.search])
    if args.summary:
        cmd.append('--summary')
    if args.interactive:
        cmd.append('--interactive')
    if args.list:
        cmd.append('--list')
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n⏹️ 日志查看器被中断")
    except Exception as e:
        print(f"❌ 运行日志查看器失败: {e}")

def run_task_monitor(args):
    """运行任务监控器"""
    script_path = Path(__file__).parent / "utils" / "arq_task_monitor.py"
    cmd = [sys.executable, str(script_path)]
    
    if args.interval:
        cmd.extend(['--interval', str(args.interval)])
    if args.dashboard:
        cmd.append('--dashboard')
    if args.cleanup:
        cmd.append('--cleanup')
    if args.once:
        cmd.append('--once')
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n⏹️ 任务监控器被中断")
    except Exception as e:
        print(f"❌ 运行任务监控器失败: {e}")

def run_full_monitor(args):
    """运行完整监控器"""
    script_path = Path(__file__).parent / "utils" / "arq_monitor.py"
    cmd = [sys.executable, str(script_path)]
    
    if args.log_level:
        cmd.extend(['--log-level', args.log_level])
    if args.interval:
        cmd.extend(['--interval', str(args.interval)])
    if args.dashboard:
        cmd.append('--dashboard')
    if args.export:
        cmd.extend(['--export', args.export])
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n⏹️ 完整监控器被中断")
    except Exception as e:
        print(f"❌ 运行完整监控器失败: {e}")

def show_quick_status():
    """显示快速状态"""
    print("📊 快速状态检查")
    print("-" * 40)

    # 检查Redis连接
    try:
        # 直接在当前进程中测试Redis连接
        import asyncio

        async def test_redis_connection():
            try:
                # 添加当前目录到Python路径
                import sys
                current_dir = Path(__file__).parent
                if str(current_dir) not in sys.path:
                    sys.path.insert(0, str(current_dir))

                from config.redis_config import redis_manager
                await redis_manager.initialize()
                await redis_manager.redis.ping()

                # 获取基本信息
                info = await redis_manager.redis.info()
                queue_length = await redis_manager.redis.llen('arq:capture_tasks')

                await redis_manager.close()

                return True, {
                    'version': info.get('redis_version', 'unknown'),
                    'memory': info.get('used_memory_human', 'unknown'),
                    'clients': info.get('connected_clients', 0),
                    'queue_length': queue_length
                }
            except Exception as e:
                return False, str(e)

        success, result = asyncio.run(test_redis_connection())

        if success:
            print("✅ Redis连接正常")
            print(f"  版本: {result['version']}")
            print(f"  内存使用: {result['memory']}")
            print(f"  连接数: {result['clients']}")
            print(f"  队列长度: {result['queue_length']}")
        else:
            print("❌ Redis连接失败")
            print(f"  错误: {result}")

    except Exception as e:
        print(f"❌ 检查Redis连接失败: {e}")
    
    # 检查日志文件
    log_dir = Path("logs")
    if log_dir.exists():
        worker_logs = list((log_dir / "worker").glob("*.log")) if (log_dir / "worker").exists() else []
        app_logs = list((log_dir / "app").glob("*.log")) if (log_dir / "app").exists() else []
        error_logs = list((log_dir / "error").glob("*.log")) if (log_dir / "error").exists() else []
        
        print(f"📁 日志文件统计:")
        print(f"  Worker日志: {len(worker_logs)} 个文件")
        print(f"  应用日志: {len(app_logs)} 个文件")
        print(f"  错误日志: {len(error_logs)} 个文件")
        
        if worker_logs:
            latest_worker_log = max(worker_logs, key=lambda f: f.stat().st_mtime)
            print(f"  最新Worker日志: {latest_worker_log.name}")
    else:
        print("❌ 日志目录不存在")

def show_help():
    """显示帮助信息"""
    print("🎯 ARQ监控工具使用指南")
    print("-" * 40)
    print()
    print("1. 日志查看器 (logs):")
    print("   python monitor_arq.py logs --follow          # 实时跟踪最新日志")
    print("   python monitor_arq.py logs --tail 100        # 显示最后100行")
    print("   python monitor_arq.py logs --search error    # 搜索错误信息")
    print("   python monitor_arq.py logs --interactive     # 交互模式")
    print("   python monitor_arq.py logs --summary         # 日志摘要")
    print()
    print("2. 任务监控器 (tasks):")
    print("   python monitor_arq.py tasks --dashboard      # 实时监控面板")
    print("   python monitor_arq.py tasks --once           # 单次状态检查")
    print("   python monitor_arq.py tasks --cleanup        # 清理孤儿任务")
    print()
    print("3. 完整监控器 (monitor):")
    print("   python monitor_arq.py monitor --dashboard    # 完整监控面板")
    print("   python monitor_arq.py monitor --export file  # 导出监控数据")
    print()
    print("4. 快速状态 (status):")
    print("   python monitor_arq.py status                 # 快速状态检查")
    print()
    print("5. 常用组合:")
    print("   # 开发调试时的实时监控")
    print("   python monitor_arq.py tasks --dashboard")
    print()
    print("   # 查看最新错误日志")
    print("   python monitor_arq.py logs --type error --tail 50")
    print()
    print("   # 搜索特定任务的日志")
    print("   python monitor_arq.py logs --search 'mysql_capture_task'")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ARQ监控工具集合')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 日志查看器子命令
    logs_parser = subparsers.add_parser('logs', help='日志查看器')
    logs_parser.add_argument('--type', dest='log_type', choices=['worker', 'startup', 'app', 'debug', 'error'],
                           default='worker', help='日志类型')
    logs_parser.add_argument('--tail', type=int, help='显示最后几行')
    logs_parser.add_argument('--follow', action='store_true', help='实时跟踪日志')
    logs_parser.add_argument('--search', help='搜索模式')
    logs_parser.add_argument('--summary', action='store_true', help='显示日志摘要')
    logs_parser.add_argument('--interactive', action='store_true', help='交互模式')
    logs_parser.add_argument('--list', action='store_true', help='列出日志文件')
    
    # 任务监控器子命令
    tasks_parser = subparsers.add_parser('tasks', help='任务监控器')
    tasks_parser.add_argument('--interval', type=int, default=5, help='监控更新间隔（秒）')
    tasks_parser.add_argument('--dashboard', action='store_true', help='显示实时监控面板')
    tasks_parser.add_argument('--cleanup', action='store_true', help='清理过期任务')
    tasks_parser.add_argument('--once', action='store_true', help='只运行一次')
    
    # 完整监控器子命令
    monitor_parser = subparsers.add_parser('monitor', help='完整监控器')
    monitor_parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                               help='日志级别')
    monitor_parser.add_argument('--interval', type=int, default=5, help='监控更新间隔（秒）')
    monitor_parser.add_argument('--dashboard', action='store_true', help='显示实时监控面板')
    monitor_parser.add_argument('--export', help='导出监控数据到文件')
    
    # 快速状态子命令
    status_parser = subparsers.add_parser('status', help='快速状态检查')
    
    # 帮助子命令
    help_parser = subparsers.add_parser('help', help='显示详细帮助')
    
    args = parser.parse_args()
    
    # 如果没有提供命令，显示帮助
    if not args.command:
        print_banner()
        show_help()
        return 0
    
    print_banner()
    
    try:
        if args.command == 'logs':
            run_log_viewer(args)
        elif args.command == 'tasks':
            run_task_monitor(args)
        elif args.command == 'monitor':
            run_full_monitor(args)
        elif args.command == 'status':
            show_quick_status()
        elif args.command == 'help':
            show_help()
        else:
            print(f"❌ 未知命令: {args.command}")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序出错: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    # 确保在正确的目录中运行
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    sys.exit(main())
