"""
测试用例管理数据模型
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import uuid
from utils.timezone_utils import format_time, to_china_timezone


class PriorityEnum(str, Enum):
    """优先级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class StatusEnum(str, Enum):
    """状态枚举"""
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    FAILED = "failed"
    DEPRECATED = "deprecated"
    ARCHIVED = "archived"


class ReviewStatusEnum(str, Enum):
    """评审状态枚举"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"


class ExecutionResultEnum(str, Enum):
    """执行结果枚举"""
    PASS = "pass"
    FAIL = "fail"
    BLOCKED = "blocked"
    SKIP = "skip"


class AutomationLevelEnum(str, Enum):
    """自动化程度枚举"""
    MANUAL = "manual"
    SEMI_AUTO = "semi_auto"
    AUTO = "auto"


class DatabaseTypeEnum(str, Enum):
    """数据库类型枚举"""
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    MONGODB = "mongodb"
    ORACLE = "oracle"
    GAUSSDB = "gaussdb"


class OperationTypeEnum(str, Enum):
    """操作类型枚举"""
    QUERY = "查询"
    INSERT = "插入"
    UPDATE = "更新"
    DELETE = "删除"
    CREATE = "创建"
    TRANSACTION = "事务"
    PROCEDURE = "存储过程"
    COMPREHENSIVE = "综合"  # 新增综合类型


# 数据库版本配置
DATABASE_VERSIONS = {
    "mysql": [
        "MySQL 8.0",
        "MySQL 5.7",
        "MySQL 8.1",
        "MySQL 8.2",
        "MySQL 5.6"
    ],
    "postgresql": [
        "PostgreSQL 15",
        "PostgreSQL 14",
        "PostgreSQL 13",
        "PostgreSQL 12",
        "PostgreSQL 16"
    ],
    "mongodb": [
        "MongoDB 7.0",
        "MongoDB 6.0",
        "MongoDB 5.0",
        "MongoDB 4.4",
        "MongoDB 8.0"
    ],
    "oracle": [
        "Oracle 21c",
        "Oracle 19c",
        "Oracle 18c",
        "Oracle 12c",
        "Oracle 23c"
    ],
    "gaussdb": [
        "GaussDB 5.0",
        "GaussDB 3.0",
        "GaussDB 2.0",
        "GaussDB 1.0",
        "openGauss 6.0"
    ]
}


class TestStep(BaseModel):
    """测试步骤模型"""
    step_number: int = Field(..., description="步骤序号")
    action: str = Field(..., description="操作描述")
    expected_result: str = Field(..., description="预期结果")
    test_data: Optional[str] = Field(None, description="测试数据")
    sql_statement: Optional[str] = Field(None, description="SQL语句")
    expected_packets: Optional[int] = Field(None, description="预期数据包数量")


class PacketPattern(BaseModel):
    """数据包模式模型"""
    pattern_type: str = Field(..., description="模式类型(query/response/connection)")
    pattern_content: str = Field(..., description="模式内容")
    min_occurrences: int = Field(1, description="最小出现次数")
    max_occurrences: Optional[int] = Field(None, description="最大出现次数")


class PacketValidationRule(BaseModel):
    """数据包验证规则模型"""
    rule_name: str = Field(..., description="规则名称")
    rule_type: str = Field(..., description="规则类型(packet_count/content_match/timing)")
    rule_condition: str = Field(..., description="规则条件")
    expected_value: str = Field(..., description="期望值")
    tolerance: Optional[str] = Field(None, description="容差范围")


class TestCaseManagementCreate(BaseModel):
    """创建测试用例请求模型"""
    title: str = Field(..., min_length=1, max_length=255, description="测试用例标题")
    module: str = Field(..., min_length=1, max_length=100, description="所属模块")
    priority: PriorityEnum = Field(PriorityEnum.MEDIUM, description="优先级")
    status: StatusEnum = Field(StatusEnum.DRAFT, description="状态")
    preconditions: Optional[str] = Field(None, description="前置条件")
    test_steps: List[TestStep] = Field(..., min_items=1, description="测试步骤")
    expected_result: Optional[str] = Field(None, description="整体预期结果")
    test_data: Optional[Dict[str, Any]] = Field(None, description="测试数据")
    tags: Optional[List[str]] = Field(None, description="标签")
    author: Optional[str] = Field(None, max_length=100, description="创建者")
    reviewer: Optional[str] = Field(None, max_length=100, description="评审者")
    review_status: ReviewStatusEnum = Field(ReviewStatusEnum.PENDING, description="评审状态")
    review_comments: Optional[str] = Field(None, description="评审意见")
    estimated_time: int = Field(0, ge=0, description="预估执行时间(分钟)")
    automation_level: AutomationLevelEnum = Field(AutomationLevelEnum.MANUAL, description="自动化程度")
    test_environment: Optional[str] = Field(None, max_length=100, description="测试环境")
    database_type: DatabaseTypeEnum = Field(DatabaseTypeEnum.MYSQL, description="数据库类型")
    database_version: Optional[str] = Field(None, max_length=50, description="数据库版本")
    operation_type: Optional[str] = Field(None, max_length=50, description="操作类型")
    target_database_config_id: Optional[int] = Field(None, description="目标数据库配置ID")
    sql_statements: Optional[List[str]] = Field(None, description="SQL语句列表，用于自动化执行")
    expected_packet_patterns: Optional[List[PacketPattern]] = Field(None, description="预期的数据包模式")
    packet_validation_rules: Optional[List[PacketValidationRule]] = Field(None, description="数据包验证规则")
    capture_duration: int = Field(30, ge=1, le=300, description="抓包持续时间(秒)")
    auto_execute: bool = Field(False, description="是否支持自动执行")
    related_requirements: Optional[str] = Field(None, description="关联需求")
    related_defects: Optional[str] = Field(None, description="关联缺陷")
    notes: Optional[str] = Field(None, description="备注")


class TestCaseManagementUpdate(BaseModel):
    """更新测试用例请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="测试用例标题")
    module: Optional[str] = Field(None, min_length=1, max_length=100, description="所属模块")
    priority: Optional[PriorityEnum] = Field(None, description="优先级")
    status: Optional[StatusEnum] = Field(None, description="状态")
    preconditions: Optional[str] = Field(None, description="前置条件")
    test_steps: Optional[List[TestStep]] = Field(None, min_items=1, description="测试步骤")
    expected_result: Optional[str] = Field(None, description="整体预期结果")
    test_data: Optional[Dict[str, Any]] = Field(None, description="测试数据")
    tags: Optional[List[str]] = Field(None, description="标签")
    author: Optional[str] = Field(None, max_length=100, description="创建者")
    reviewer: Optional[str] = Field(None, max_length=100, description="评审者")
    review_status: Optional[ReviewStatusEnum] = Field(None, description="评审状态")
    review_comments: Optional[str] = Field(None, description="评审意见")
    estimated_time: Optional[int] = Field(None, ge=0, description="预估执行时间(分钟)")
    automation_level: Optional[AutomationLevelEnum] = Field(None, description="自动化程度")
    test_environment: Optional[str] = Field(None, max_length=100, description="测试环境")
    database_type: Optional[DatabaseTypeEnum] = Field(None, description="数据库类型")
    target_database_config_id: Optional[int] = Field(None, description="目标数据库配置ID")
    sql_statements: Optional[List[str]] = Field(None, description="SQL语句列表，用于自动化执行")
    expected_packet_patterns: Optional[List[PacketPattern]] = Field(None, description="预期的数据包模式")
    packet_validation_rules: Optional[List[PacketValidationRule]] = Field(None, description="数据包验证规则")
    capture_duration: Optional[int] = Field(None, ge=1, le=300, description="抓包持续时间(秒)")
    auto_execute: Optional[bool] = Field(None, description="是否支持自动执行")
    related_requirements: Optional[str] = Field(None, description="关联需求")
    related_defects: Optional[str] = Field(None, description="关联缺陷")
    notes: Optional[str] = Field(None, description="备注")


class TestCaseManagementResponse(BaseModel):
    """测试用例响应模型"""
    id: str = Field(..., description="测试用例UUID")
    title: str = Field(..., description="测试用例标题")
    module: str = Field(..., description="所属模块")
    priority: PriorityEnum = Field(..., description="优先级")
    status: StatusEnum = Field(..., description="状态")
    preconditions: Optional[str] = Field(None, description="前置条件")
    test_steps: List[TestStep] = Field(..., description="测试步骤")
    expected_result: Optional[str] = Field(None, description="整体预期结果")
    test_data: Optional[Dict[str, Any]] = Field(None, description="测试数据")
    tags: Optional[List[str]] = Field(None, description="标签")
    author: Optional[str] = Field(None, description="创建者")
    reviewer: Optional[str] = Field(None, description="评审者")
    review_status: ReviewStatusEnum = Field(..., description="评审状态")
    review_comments: Optional[str] = Field(None, description="评审意见")
    execution_count: int = Field(..., description="执行次数")
    success_count: int = Field(..., description="成功次数")
    failure_count: int = Field(..., description="失败次数")
    last_execution_time: Optional[datetime] = Field(None, description="最后执行时间")
    last_execution_result: Optional[ExecutionResultEnum] = Field(None, description="最后执行结果")
    last_execution_id: Optional[str] = Field(None, description="最后执行记录ID")
    last_execution_config_id: Optional[int] = Field(None, description="最后执行使用的数据库配置ID")
    last_execution_capture_files: Optional[List[str]] = Field(None, description="最后执行的抓包文件列表")
    last_execution_duration: int = Field(0, description="最后执行耗时(秒)")
    estimated_time: int = Field(..., description="预估执行时间(分钟)")
    actual_time: int = Field(..., description="实际执行时间(分钟)")
    automation_level: AutomationLevelEnum = Field(..., description="自动化程度")
    test_environment: Optional[str] = Field(None, description="测试环境")
    database_type: DatabaseTypeEnum = Field(..., description="数据库类型")
    target_database_config_id: Optional[int] = Field(None, description="目标数据库配置ID")
    sql_statements: Optional[List[str]] = Field(None, description="SQL语句列表")
    expected_packet_patterns: Optional[List[PacketPattern]] = Field(None, description="预期的数据包模式")
    packet_validation_rules: Optional[List[PacketValidationRule]] = Field(None, description="数据包验证规则")
    capture_duration: int = Field(..., description="抓包持续时间(秒)")
    auto_execute: bool = Field(..., description="是否支持自动执行")
    related_requirements: Optional[str] = Field(None, description="关联需求")
    related_defects: Optional[str] = Field(None, description="关联缺陷")
    notes: Optional[str] = Field(None, description="备注")
    database_version: Optional[str] = Field(None, description="数据库版本")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class TestCaseManagementQuery(BaseModel):
    """测试用例查询参数模型"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    keyword: Optional[str] = Field(None, description="关键词搜索")
    module: Optional[str] = Field(None, description="模块筛选")
    priority: Optional[PriorityEnum] = Field(None, description="优先级筛选")
    status: Optional[StatusEnum] = Field(None, description="状态筛选")
    author: Optional[str] = Field(None, description="创建者筛选")
    reviewer: Optional[str] = Field(None, description="评审者筛选")
    review_status: Optional[ReviewStatusEnum] = Field(None, description="评审状态筛选")
    automation_level: Optional[AutomationLevelEnum] = Field(None, description="自动化程度筛选")
    database_type: Optional[DatabaseTypeEnum] = Field(None, description="数据库类型筛选")
    database_version: Optional[str] = Field(None, description="数据库版本筛选")
    auto_execute: Optional[bool] = Field(None, description="自动执行筛选")
    tags: Optional[List[str]] = Field(None, description="标签筛选")
    created_start: Optional[datetime] = Field(None, description="创建时间开始")
    created_end: Optional[datetime] = Field(None, description="创建时间结束")


class TestCaseManagementListResponse(BaseModel):
    """测试用例列表响应模型"""
    items: List[TestCaseManagementResponse] = Field(..., description="测试用例列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")


class TestCaseExecutionRecord(BaseModel):
    """测试用例执行记录模型"""
    execution_result: ExecutionResultEnum = Field(..., description="执行结果")
    execution_time: int = Field(..., ge=0, description="执行时间(分钟)")
    execution_notes: Optional[str] = Field(None, description="执行备注")
    executor: Optional[str] = Field(None, description="执行者")
    execution_environment: Optional[str] = Field(None, description="执行环境")
    defects_found: Optional[List[str]] = Field(None, description="发现的缺陷")

    # 内部使用字段，不在API请求中
    test_case_id: Optional[str] = Field(None, description="测试用例ID")


class TestCaseLastExecution(BaseModel):
    """测试用例最近执行记录"""
    execution_id: str = Field(..., description="执行记录ID")
    execution_time: datetime = Field(..., description="执行时间")
    execution_result: ExecutionResultEnum = Field(..., description="执行结果")
    database_config_id: Optional[int] = Field(None, description="数据库配置ID")
    database_config_name: Optional[str] = Field(None, description="数据库配置名称")
    capture_files: List[str] = Field(default_factory=list, description="抓包文件列表")
    duration: int = Field(0, description="执行耗时(秒)")
    success_rate: float = Field(0.0, description="成功率")
    # 新增：完整的执行详情
    execution_details: Optional[Dict[str, Any]] = Field(None, description="完整的执行结果详情")

    class Config:
        json_encoders = {
            datetime: lambda v: format_time(to_china_timezone(v)) if v else None
        }


class TestCaseStatistics(BaseModel):
    """测试用例统计模型"""
    total_count: int = Field(..., description="总数量")
    status_distribution: Dict[str, int] = Field(..., description="状态分布")
    priority_distribution: Dict[str, int] = Field(..., description="优先级分布")
    automation_distribution: Dict[str, int] = Field(..., description="自动化程度分布")
    module_distribution: Dict[str, int] = Field(..., description="模块分布")
    execution_rate: float = Field(..., description="执行率")
    pass_rate: float = Field(..., description="通过率")


# 批量执行相关数据模型

class BatchExecutionStatusEnum(str, Enum):
    """批量执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PARTIAL_SUCCESS = "partial_success"


class TestCaseExecutionItem(BaseModel):
    """测试用例执行项"""
    test_case_id: str = Field(..., description="测试用例ID")
    test_case_title: str = Field(..., description="测试用例标题")
    test_case_json: str = Field(..., description="测试用例JSON内容")
    status: str = Field("pending", description="执行状态")
    start_time: Optional[str] = Field(None, description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")
    duration: Optional[int] = Field(None, description="执行耗时(秒)")
    success: Optional[bool] = Field(None, description="是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")
    capture_files: Optional[List[str]] = Field(None, description="抓包文件列表")
    execution_result: Optional[Dict[str, Any]] = Field(None, description="执行结果详情")


class BatchExecutionRequest(BaseModel):
    """批量执行请求"""
    name: str = Field(..., min_length=1, max_length=255, description="批量执行任务名称")
    database_type: str = Field(..., description="数据库类型")
    database_version: str = Field(..., description="数据库版本")
    config_id: int = Field(..., description="数据库配置ID")
    test_case_items: List[TestCaseExecutionItem] = Field(..., min_items=1, description="测试用例列表")
    capture_enabled: bool = Field(True, description="是否启用抓包")
    timeout_per_case: int = Field(600, ge=60, le=1800, description="单个用例超时时间(秒)")
    stop_on_failure: bool = Field(False, description="遇到失败时是否停止")
    use_c_executor: bool = Field(False, description="是否使用C语言执行器（仅适用于GaussDB）")
    description: Optional[str] = Field(None, description="批量执行描述")


class BatchExecutionResponse(BaseModel):
    """批量执行响应"""
    batch_id: str = Field(..., description="批量执行ID")
    name: str = Field(..., description="批量执行任务名称")
    database_type: str = Field(..., description="数据库类型")
    database_version: str = Field(..., description="数据库版本")
    config_id: int = Field(..., description="数据库配置ID")
    status: BatchExecutionStatusEnum = Field(..., description="批量执行状态")
    total_cases: int = Field(..., description="总用例数")
    completed_cases: int = Field(0, description="已完成用例数")
    success_cases: int = Field(0, description="成功用例数")
    failed_cases: int = Field(0, description="失败用例数")
    progress: float = Field(0.0, ge=0.0, le=1.0, description="执行进度(0-1)")
    start_time: Optional[str] = Field(None, description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")
    duration: Optional[int] = Field(None, description="总耗时(秒)")
    test_case_items: List[TestCaseExecutionItem] = Field(..., description="测试用例执行详情")
    capture_enabled: bool = Field(..., description="是否启用抓包")
    timeout_per_case: int = Field(..., description="单个用例超时时间(秒)")
    stop_on_failure: bool = Field(..., description="遇到失败时是否停止")
    description: Optional[str] = Field(None, description="批量执行描述")
    error_message: Optional[str] = Field(None, description="批量执行错误信息")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class BatchExecutionListResponse(BaseModel):
    """批量执行列表响应"""
    batch_executions: List[BatchExecutionResponse] = Field(..., description="批量执行列表")
    total: int = Field(..., description="总数量")
    page: int = Field(1, description="页码")
    page_size: int = Field(20, description="每页数量")
    total_pages: int = Field(1, description="总页数")


class BatchExecutionQuery(BaseModel):
    """批量执行查询参数"""
    database_type: Optional[str] = Field(None, description="数据库类型过滤")
    database_version: Optional[str] = Field(None, description="数据库版本过滤")
    status: Optional[BatchExecutionStatusEnum] = Field(None, description="状态过滤")
    start_date: Optional[str] = Field(None, description="开始日期过滤")
    end_date: Optional[str] = Field(None, description="结束日期过滤")
    keyword: Optional[str] = Field(None, description="关键词搜索")


class BatchExecutionStatistics(BaseModel):
    """批量执行统计"""
    total_batches: int = Field(..., description="总批量执行数")
    running_batches: int = Field(..., description="运行中批量执行数")
    completed_batches: int = Field(..., description="已完成批量执行数")
    failed_batches: int = Field(..., description="失败批量执行数")
    total_test_cases: int = Field(..., description="总测试用例数")
    success_test_cases: int = Field(..., description="成功测试用例数")
    failed_test_cases: int = Field(..., description="失败测试用例数")
    average_success_rate: float = Field(..., description="平均成功率")
    database_type_distribution: Dict[str, int] = Field(..., description="数据库类型分布")
    recent_executions: List[BatchExecutionResponse] = Field(..., description="最近执行记录")
