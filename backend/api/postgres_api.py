from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import logging
import traceback
import time
import os
import asyncio

from services.postgres_ai_service import PostgresAIService
from services.postgres_service import PostgresService
from services.postgres_local_packet_capture_service import PostgresLocalPacketCaptureService
from services.database_config_service import DatabaseConfigService
from utils.config import Config

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/postgres", tags=["postgresql"])

# 请求和响应模型
class PostgresQueryRequest(BaseModel):
    query: str
    capture_packets: bool = True
    config_id: Optional[int] = None

class PostgresQueryResponse(BaseModel):
    success: bool
    sql_query: Optional[str] = None
    result: Optional[Any] = None
    packet_file: Optional[str] = None
    error: Optional[str] = None

class PostgresCaptureAnalyzeRequest(BaseModel):
    file_path: str

class PostgresQueryAnalyzeRequest(BaseModel):
    query: str

class PostgresExplainRequest(BaseModel):
    sql_query: str
    result: Dict[str, Any]

class PostgresOptimizeRequest(BaseModel):
    sql_query: str

class PostgresConnectionTestRequest(BaseModel):
    config_id: Optional[int] = None

# 服务实例
postgres_ai_service = PostgresAIService()
postgres_service = PostgresService()
packet_service = PostgresLocalPacketCaptureService()
db_config_service = DatabaseConfigService()

@router.post("/query", response_model=PostgresQueryResponse)
async def process_postgres_query(request: PostgresQueryRequest):
    """处理PostgreSQL自然语言查询"""
    try:
        logger.info(f"Processing PostgreSQL query: {request.query}")

        # 1. 使用AI解析自然语言为PostgreSQL SQL查询
        parsed_result = await postgres_ai_service.parse_natural_language_to_sql(request.query)
        logger.info(f"Generated PostgreSQL SQL: {parsed_result}")

        packet_file = None

        # 获取PostgreSQL数据库配置（在函数开始就获取，供后续使用）
        postgres_config = None
        if request.config_id:
            postgres_config = await db_config_service.get_config(request.config_id)
            if postgres_config:
                logger.info(f"Using PostgreSQL config: {postgres_config.name} ({postgres_config.host}:{postgres_config.port})")

        # 2. 如果需要抓包，启动智能抓包服务
        if request.capture_packets:
            try:

                # 获取默认服务器配置用于抓包
                from services.server_config_service import server_config_service
                default_server_config = await server_config_service.get_default_config()
                server_config_id = default_server_config.id if default_server_config else None

                # 使用智能抓包功能，传入PostgreSQL目标主机和端口
                if postgres_config:
                    packet_file = await packet_service.start_smart_capture(
                        target_host=postgres_config.host,
                        target_port=postgres_config.port,
                        server_config_id=server_config_id
                    )
                else:
                    # 使用默认配置
                    packet_file = await packet_service.start_smart_capture(
                        server_config_id=server_config_id
                    )
                logger.info(f"Started PostgreSQL smart packet capture: {packet_file}")

                # 等待抓包服务完全启动
                await asyncio.sleep(2)
            except Exception as e:
                logger.error(f"Smart capture failed, falling back to regular capture: {str(e)}")
                # 回退到常规抓包
                if postgres_config:
                    packet_file = await packet_service.start_capture(
                        target_host=postgres_config.host,
                        target_port=postgres_config.port
                    )
                else:
                    packet_file = await packet_service.start_capture()
                logger.info(f"Started PostgreSQL regular packet capture: {packet_file}")

                # 等待抓包服务完全启动
                await asyncio.sleep(2)

        # 3. 执行SQL查询（如果需要）
        if not request.capture_packets:
            # 如果不需要抓包，只返回解析的SQL，不执行查询
            logger.info("PostgreSQL query parsed only (not executed)")
            return PostgresQueryResponse(
                success=True,
                sql_query=parsed_result if isinstance(parsed_result, str) else str(parsed_result),
                result={'message': 'SQL parsed successfully (not executed)', 'response_type': 'PARSE_ONLY'},
                packet_file=None
            )
        else:
            # 根据配置创建PostgreSQL服务实例并执行查询
            if request.config_id:
                # 使用指定的数据库配置
                config = await db_config_service.get_config(request.config_id)
                if not config or config.database_type != 'postgresql':
                    raise HTTPException(status_code=400, detail="PostgreSQL配置不存在或类型不匹配")

                query_service = PostgresService(
                    host=config.host,
                    port=config.port,
                    user=config.user,
                    password=config.password,
                    database=config.database_name
                )
                await query_service.initialize()
            else:
                # 使用默认配置
                query_service = postgres_service
                await query_service.initialize()

            # 执行SQL查询（使用手动连接控制以确保抓包质量）
            try:
                if request.capture_packets:
                    # 如果需要抓包，使用专门的抓包执行方法
                    result = await query_service.execute_query_for_capture(parsed_result)
                    logger.info(f"PostgreSQL query executed for capture: {result}")

                    # 强制关闭连接以确保捕获挥手包
                    await query_service.force_close_connection()
                    logger.info("PostgreSQL connection force closed for packet capture")
                else:
                    # 如果不需要抓包，使用常规方法
                    result = await query_service.execute_sql_query(parsed_result)
                    logger.info(f"PostgreSQL query executed: {result}")
            except Exception as e:
                logger.error(f"PostgreSQL query execution failed: {str(e)}")
                # 确保连接被关闭
                if request.capture_packets:
                    try:
                        await query_service.force_close_connection()
                    except Exception as close_e:
                        logger.warning(f"Failed to force close PostgreSQL connection: {str(close_e)}")
                raise

        # 4. 停止抓包并获取最终的文件路径
        if request.capture_packets:
            final_packet_file = await packet_service.stop_capture()
            if final_packet_file:
                packet_file = final_packet_file
            logger.info(f"Stopped PostgreSQL packet capture, final file: {packet_file}")

            # 5. 检查抓包质量，如果是空包则重试
            if packet_file and os.path.exists(packet_file):
                file_size = os.path.getsize(packet_file)
                logger.info(f"Capture file size: {file_size} bytes")

                if file_size <= 24:
                    logger.warning("Detected empty capture file, attempting smart retry")
                    try:
                        # 清理当前抓包状态
                        try:
                            await packet_service.stop_capture()
                        except Exception as e:

                            logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                            logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                        # 使用智能抓包重试，传入正确的目标主机和端口
                        if postgres_config:
                            retry_packet_file = await packet_service.start_smart_capture(
                                target_host=postgres_config.host,
                                target_port=postgres_config.port,
                                server_config_id=server_config_id
                            )
                        else:
                            retry_packet_file = await packet_service.start_smart_capture(
                                server_config_id=server_config_id
                            )

                        if retry_packet_file:
                            logger.info(f"Smart retry capture started: {retry_packet_file}")

                            # 等待一段时间让抓包启动
                            await asyncio.sleep(3)

                            # 重新执行查询以产生流量
                            logger.info("Re-executing query to generate traffic for retry capture")
                            await postgres_service.execute_sql_query(parsed_result)

                            # 等待流量产生
                            await asyncio.sleep(2)

                            # 停止重试抓包
                            final_retry_file = await packet_service.stop_capture()
                            if final_retry_file and os.path.exists(final_retry_file):
                                retry_file_size = os.path.getsize(final_retry_file)
                                logger.info(f"Retry capture file size: {retry_file_size} bytes")

                                if retry_file_size > 24:
                                    packet_file = final_retry_file
                                    logger.info(f"Smart retry successful: {packet_file}")
                                else:
                                    logger.warning("Smart retry also produced empty capture")
                            else:
                                logger.warning("Smart retry did not produce a valid file")
                    except Exception as e:
                        logger.error(f"Smart retry failed: {str(e)}")
                        # 继续使用原始文件

        return PostgresQueryResponse(
            success=True,
            sql_query=parsed_result if isinstance(parsed_result, str) else str(parsed_result),
            result=result,
            packet_file=packet_file
        )

    except Exception as e:
        logger.error(f"Error processing PostgreSQL query: {str(e)}")
        # 确保停止抓包
        if request.capture_packets:
            await packet_service.stop_capture()

        return PostgresQueryResponse(
            success=False,
            error=str(e)
        )

@router.get("/health")
async def check_postgres_health():
    """检查PostgreSQL健康状态"""
    try:
        is_connected = await postgres_service.check_connection()
        return {
            "status": "healthy" if is_connected else "unhealthy",
            "connected": is_connected,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"PostgreSQL health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "connected": False,
            "error": str(e),
            "timestamp": time.time()
        }

@router.post("/test-connection")
async def test_postgres_connection(request: PostgresConnectionTestRequest):
    """测试PostgreSQL连接"""
    try:
        # 获取PostgreSQL服务实例
        if request.config_id:
            # 使用指定配置
            config = await db_config_service.get_config(request.config_id)
            if not config or config.database_type != 'postgresql':
                return {
                    "success": False,
                    "error": "PostgreSQL配置不存在或类型不匹配"
                }

            test_service = PostgresService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )
        else:
            # 使用默认配置
            test_service = postgres_service

        is_connected = await test_service.check_connection()
        if is_connected:
            # 获取数据库和表信息
            databases = await test_service.get_databases()
            tables = await test_service.get_tables()

            return {
                "success": True,
                "databases": databases,
                "tables": tables
            }
        else:
            return {
                "success": False,
                "error": "PostgreSQL connection failed"
            }
    except Exception as e:
        logger.error(f"PostgreSQL connection test failed: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/databases")
async def get_postgres_databases():
    """获取PostgreSQL数据库列表"""
    try:
        databases = await postgres_service.get_databases()
        return {"databases": databases}
    except Exception as e:
        logger.error(f"Failed to get PostgreSQL databases: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tables")
async def get_postgres_tables(schema: str = 'public', config_id: Optional[int] = None):
    """获取PostgreSQL表列表"""
    try:
        if config_id:
            # 使用指定的数据库配置
            config = await db_config_service.get_config(config_id)
            if not config or config.database_type != 'postgresql':
                raise HTTPException(status_code=400, detail="PostgreSQL配置不存在或类型不匹配")

            query_service = PostgresService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )
            await query_service.initialize()
            tables = await query_service.get_tables(schema)
        else:
            # 使用默认配置
            tables = await postgres_service.get_tables(schema)

        return {"tables": tables}
    except Exception as e:
        logger.error(f"Failed to get PostgreSQL tables: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/table/{table_name}")
async def get_postgres_table_info(table_name: str, schema: str = 'public'):
    """获取PostgreSQL表结构信息"""
    try:
        table_info = await postgres_service.get_table_info(table_name, schema)
        return {"table_info": table_info}
    except Exception as e:
        logger.error(f"Failed to get PostgreSQL table info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/capture/status")
async def get_postgres_capture_status():
    """获取PostgreSQL抓包状态"""
    return packet_service.get_capture_status()

@router.post("/capture/start")
async def start_postgres_capture():
    """手动启动PostgreSQL抓包"""
    try:
        packet_file = await packet_service.start_capture()
        return {"success": True, "packet_file": packet_file}
    except Exception as e:
        logger.error(f"Failed to start PostgreSQL capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/capture/stop")
async def stop_postgres_capture():
    """手动停止PostgreSQL抓包"""
    try:
        packet_file = await packet_service.stop_capture()
        return {"success": True, "packet_file": packet_file}
    except Exception as e:
        logger.error(f"Failed to stop PostgreSQL capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/capture/analyze")
async def analyze_postgres_capture(request: PostgresCaptureAnalyzeRequest):
    """分析PostgreSQL抓包文件"""
    try:
        analysis = await packet_service.analyze_capture_file(request.file_path)
        return {"success": True, "analysis": analysis}
    except Exception as e:
        logger.error(f"Failed to analyze PostgreSQL capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query/analyze")
async def analyze_postgres_query_intent(request: PostgresQueryAnalyzeRequest):
    """分析PostgreSQL查询意图"""
    try:
        analysis = await postgres_ai_service.analyze_postgres_query_intent(request.query)
        return {"success": True, "analysis": analysis}
    except Exception as e:
        logger.error(f"Failed to analyze PostgreSQL query intent: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query/explain")
async def explain_postgres_query_result(request: PostgresExplainRequest):
    """解释PostgreSQL查询结果"""
    try:
        explanation = await postgres_ai_service.generate_postgres_explanation(
            request.sql_query, request.result
        )
        return {"success": True, "explanation": explanation}
    except Exception as e:
        logger.error(f"Failed to explain PostgreSQL query result: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query/optimize")
async def suggest_postgres_optimizations(request: PostgresOptimizeRequest):
    """建议PostgreSQL优化"""
    try:
        suggestions = await postgres_ai_service.suggest_postgres_optimizations(request.sql_query)
        return {"success": True, "suggestions": suggestions}
    except Exception as e:
        logger.error(f"Failed to suggest PostgreSQL optimizations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
