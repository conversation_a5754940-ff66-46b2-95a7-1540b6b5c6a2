"""
任务管理API
"""

import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel, Field

from services.arq_task_management_service import arq_task_management_service as task_management_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/tasks", tags=["任务管理"])

# 请求模型
class MySQLCaptureTaskRequest(BaseModel):
    config_id: int = Field(..., description="数据库配置ID")
    sql_query: str = Field(..., description="SQL查询语句")
    capture_duration: int = Field(30, description="抓包持续时间（秒）", ge=10, le=300)

class PostgresCaptureTaskRequest(BaseModel):
    config_id: int = Field(..., description="数据库配置ID")
    sql_query: str = Field(..., description="SQL查询语句")
    capture_duration: int = Field(30, description="抓包持续时间（秒）", ge=10, le=300)

class MongoCaptureTaskRequest(BaseModel):
    config_id: int = Field(..., description="数据库配置ID")
    mongo_query: str = Field(..., description="MongoDB查询语句")
    capture_duration: int = Field(30, description="抓包持续时间（秒）", ge=10, le=300)
    executor_type: str = Field("python", description="执行器类型：python 或 c")

class AIMySQLCaptureTaskRequest(BaseModel):
    config_id: int = Field(..., description="数据库配置ID")
    natural_query: str = Field(..., description="自然语言查询")
    capture_duration: int = Field(30, description="抓包持续时间（秒）", ge=10, le=300)

class AIPostgresCaptureTaskRequest(BaseModel):
    config_id: int = Field(..., description="数据库配置ID")
    natural_query: str = Field(..., description="自然语言查询")
    capture_duration: int = Field(30, description="抓包持续时间（秒）", ge=10, le=300)

class GaussDBCaptureTaskRequest(BaseModel):
    config_id: int = Field(..., description="数据库配置ID")
    sql_query: str = Field(..., description="SQL查询语句")
    capture_duration: int = Field(30, description="抓包持续时间（秒）", ge=10, le=300)

class AIGaussDBCaptureTaskRequest(BaseModel):
    config_id: int = Field(..., description="数据库配置ID")
    natural_query: str = Field(..., description="自然语言查询")
    capture_duration: int = Field(30, description="抓包持续时间（秒）", ge=10, le=300)

class AIMongoCaptureTaskRequest(BaseModel):
    config_id: int = Field(..., description="数据库配置ID")
    natural_query: str = Field(..., description="自然语言查询")
    capture_duration: int = Field(30, description="抓包持续时间（秒）", ge=10, le=300)
    executor_type: str = Field("python", description="执行器类型：python 或 c")

class OracleCaptureTaskRequest(BaseModel):
    config_id: int = Field(..., description="数据库配置ID")
    sql_query: str = Field(..., description="SQL查询语句")
    capture_duration: int = Field(30, description="抓包持续时间（秒）", ge=10, le=300)

class AIOracleCaptureTaskRequest(BaseModel):
    config_id: int = Field(..., description="数据库配置ID")
    natural_query: str = Field(..., description="自然语言查询")
    capture_duration: int = Field(30, description="抓包持续时间（秒）", ge=10, le=300)

class AITestCaseGenerationTaskRequest(BaseModel):
    requirement: str = Field(..., description="测试用例需求描述")
    database_type: str = Field("mysql", description="数据库类型")
    operation_type: str = Field("查询", description="操作类型")
    batch_size: int = Field(1, description="批量生成数量", ge=1, le=20)

# 响应模型
class TaskResponse(BaseModel):
    task_id: str
    message: str

class TaskInfoResponse(BaseModel):
    task_id: str
    type: str
    status: str
    progress: int = 0
    message: str = ""
    created_at: str
    updated_at: Optional[str] = None
    config_id: Optional[int] = None  # Docker构建任务使用server_config_id
    sql_query: Optional[str] = None
    mongo_query: Optional[str] = None
    natural_query: Optional[str] = None  # AI任务的自然语言查询
    capture_duration: Optional[int] = None  # Docker构建任务没有此字段
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    duration: Optional[float] = None
    # Docker构建任务特有字段
    server_config_id: Optional[int] = None
    build_requests: Optional[list] = None
    total_requests: Optional[int] = None
    # 批量测试用例执行任务特有字段
    batch_name: Optional[str] = None
    database_type: Optional[str] = None
    database_version: Optional[str] = None
    total_test_cases: Optional[int] = None
    capture_enabled: Optional[bool] = None
    timeout_per_case: Optional[int] = None
    stop_on_failure: Optional[bool] = None
    description: Optional[str] = None
    # 单个测试用例执行任务特有字段
    test_case_id: Optional[str] = None
    test_case_title: Optional[str] = None

class TaskListResponse(BaseModel):
    tasks: list[TaskInfoResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

@router.post("/mysql/capture", response_model=TaskResponse)
async def submit_mysql_capture_task(request: MySQLCaptureTaskRequest):
    """提交MySQL抓包任务"""
    try:
        task_id = await task_management_service.submit_mysql_capture_task(
            config_id=request.config_id,
            sql_query=request.sql_query,
            capture_duration=request.capture_duration
        )
        
        return TaskResponse(
            task_id=task_id,
            message="MySQL抓包任务已提交"
        )
        
    except Exception as e:
        logger.error(f"提交MySQL抓包任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")

@router.post("/postgres/capture", response_model=TaskResponse)
async def submit_postgres_capture_task(request: PostgresCaptureTaskRequest):
    """提交PostgreSQL抓包任务"""
    try:
        task_id = await task_management_service.submit_postgres_capture_task(
            config_id=request.config_id,
            sql_query=request.sql_query,
            capture_duration=request.capture_duration
        )
        
        return TaskResponse(
            task_id=task_id,
            message="PostgreSQL抓包任务已提交"
        )
        
    except Exception as e:
        logger.error(f"提交PostgreSQL抓包任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")

@router.post("/mongo/capture", response_model=TaskResponse)
async def submit_mongo_capture_task(request: MongoCaptureTaskRequest):
    """提交MongoDB抓包任务"""
    try:
        task_id = await task_management_service.submit_mongo_capture_task(
            config_id=request.config_id,
            mongo_query=request.mongo_query,
            capture_duration=request.capture_duration,
            executor_type=request.executor_type
        )

        return TaskResponse(
            task_id=task_id,
            message="MongoDB抓包任务已提交"
        )

    except Exception as e:
        logger.error(f"提交MongoDB抓包任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")

@router.post("/mysql/ai-capture", response_model=TaskResponse)
async def submit_ai_mysql_capture_task(request: AIMySQLCaptureTaskRequest):
    """提交AI+MySQL抓包任务（包含大模型请求）"""
    try:
        task_id = await task_management_service.submit_ai_mysql_capture_task(
            config_id=request.config_id,
            natural_query=request.natural_query,
            capture_duration=request.capture_duration
        )

        return TaskResponse(
            task_id=task_id,
            message="AI+MySQL抓包任务已提交"
        )

    except Exception as e:
        logger.error(f"Failed to submit AI+MySQL capture task: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/postgres/ai-capture", response_model=TaskResponse)
async def submit_ai_postgres_capture_task(request: AIPostgresCaptureTaskRequest):
    """提交AI+PostgreSQL抓包任务（包含大模型请求）"""
    try:
        task_id = await task_management_service.submit_ai_postgres_capture_task(
            config_id=request.config_id,
            natural_query=request.natural_query,
            capture_duration=request.capture_duration
        )

        return TaskResponse(
            task_id=task_id,
            message="AI+PostgreSQL抓包任务已提交"
        )

    except Exception as e:
        logger.error(f"Failed to submit AI+PostgreSQL capture task: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/gaussdb/capture", response_model=TaskResponse)
async def submit_gaussdb_capture_task(request: GaussDBCaptureTaskRequest):
    """提交GaussDB抓包任务"""
    try:
        task_id = await task_management_service.submit_gaussdb_capture_task(
            config_id=request.config_id,
            sql_query=request.sql_query,
            capture_duration=request.capture_duration
        )

        return TaskResponse(
            task_id=task_id,
            message="GaussDB抓包任务已提交"
        )

    except Exception as e:
        logger.error(f"提交GaussDB抓包任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")

@router.post("/gaussdb/ai-capture", response_model=TaskResponse)
async def submit_ai_gaussdb_capture_task(request: AIGaussDBCaptureTaskRequest):
    """提交AI+GaussDB抓包任务（包含大模型请求）"""
    try:
        task_id = await task_management_service.submit_ai_gaussdb_capture_task(
            config_id=request.config_id,
            natural_query=request.natural_query,
            capture_duration=request.capture_duration
        )

        return TaskResponse(
            task_id=task_id,
            message="AI+GaussDB抓包任务已提交"
        )

    except Exception as e:
        logger.error(f"Failed to submit AI+GaussDB capture task: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/mongo/ai-capture", response_model=TaskResponse)
async def submit_ai_mongo_capture_task(request: AIMongoCaptureTaskRequest):
    """提交AI+MongoDB抓包任务（包含大模型请求）"""
    try:
        task_id = await task_management_service.submit_ai_mongo_capture_task(
            config_id=request.config_id,
            natural_query=request.natural_query,
            capture_duration=request.capture_duration,
            executor_type=request.executor_type
        )

        return TaskResponse(
            task_id=task_id,
            message="AI+MongoDB抓包任务已提交"
        )

    except Exception as e:
        logger.error(f"Failed to submit AI+MongoDB capture task: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/oracle/capture", response_model=TaskResponse)
async def submit_oracle_capture_task(request: OracleCaptureTaskRequest):
    """提交Oracle抓包任务"""
    try:
        task_id = await task_management_service.submit_oracle_capture_task(
            config_id=request.config_id,
            sql_query=request.sql_query,
            capture_duration=request.capture_duration
        )

        return TaskResponse(
            task_id=task_id,
            message="Oracle抓包任务已提交"
        )

    except Exception as e:
        logger.error(f"提交Oracle抓包任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")

@router.post("/oracle/ai-capture", response_model=TaskResponse)
async def submit_ai_oracle_capture_task(request: AIOracleCaptureTaskRequest):
    """提交AI+Oracle抓包任务（包含大模型请求）"""
    try:
        task_id = await task_management_service.submit_ai_oracle_capture_task(
            config_id=request.config_id,
            natural_query=request.natural_query,
            capture_duration=request.capture_duration
        )

        return TaskResponse(
            task_id=task_id,
            message="AI+Oracle抓包任务已提交"
        )

    except Exception as e:
        logger.error(f"Failed to submit AI+Oracle capture task: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{task_id}", response_model=TaskInfoResponse)
async def get_task_info(task_id: str):
    """获取任务信息"""
    try:
        task_info = await task_management_service.get_task_info(task_id)

        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 转换TaskInfo对象为字典
        task_dict = task_info.to_dict()
        # 修正字段名映射
        task_dict['type'] = task_dict.pop('task_type', '')

        return TaskInfoResponse(**task_dict)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务信息失败: {str(e)}")

@router.get("/", response_model=TaskListResponse)
async def get_task_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    task_type: Optional[str] = Query(None, description="任务类型过滤")
):
    """获取任务列表（支持分页）"""
    try:
        tasks, total = await task_management_service.get_task_list(
            page=page,
            page_size=page_size,
            task_type=task_type
        )
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        # 转换为响应模型
        task_responses = []
        for task in tasks:
            # 转换TaskInfo对象为字典
            task_data = task.to_dict()
            # 修正字段名映射
            task_data['type'] = task_data.pop('task_type', '')

            if task_data.get('type') == 'docker_build':
                # Docker构建任务的特殊处理
                task_data['config_id'] = None  # Docker任务没有config_id
                task_data['capture_duration'] = None  # Docker任务没有capture_duration
            else:
                # 抓包任务的处理
                task_data['server_config_id'] = None
                task_data['build_requests'] = None
                task_data['total_requests'] = None
                # 确保config_id存在
                if 'config_id' not in task_data:
                    task_data['config_id'] = None
                # 确保capture_duration存在
                if 'capture_duration' not in task_data:
                    task_data['capture_duration'] = None

            task_responses.append(TaskInfoResponse(**task_data))
        
        return TaskListResponse(
            tasks=task_responses,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@router.delete("/{task_id}")
async def cancel_task(task_id: str):
    """取消任务"""
    try:
        success = await task_management_service.cancel_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")
        
        return {"message": "任务已取消"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

@router.post("/cleanup")
async def cleanup_expired_tasks(
    background_tasks: BackgroundTasks,
    task_type: Optional[str] = None,
    status: Optional[str] = None,
    hours: float = 24
):
    """清理过期任务

    Args:
        task_type: 任务类型，如果指定则只清理该类型的任务
        status: 任务状态，如果指定则只清理该状态的任务（failed, cancelled）
        hours: 过期时间（小时），默认24小时
    """
    try:
        # 在后台执行清理任务
        background_tasks.add_task(
            task_management_service.cleanup_expired_tasks,
            task_type=task_type,
            status=status,
            hours=hours
        )

        # 构建消息
        message_parts = []
        if task_type:
            message_parts.append(f"{task_type} 类型")
        if status:
            status_names = {
                "failed": "失败",
                "cancelled": "已取消"
            }
            message_parts.append(f"{status_names.get(status, status)} 状态")

        if message_parts:
            return {"message": f"清理 {' '.join(message_parts)} 的过期任务已启动"}
        else:
            return {"message": "清理所有过期任务已启动"}

    except Exception as e:
        logger.error(f"启动清理任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动清理任务失败: {str(e)}")

@router.get("/stats/summary")
async def get_task_stats():
    """获取任务统计信息"""
    try:
        # 获取所有任务
        all_tasks, total = await task_management_service.get_task_list(page=1, page_size=1000)
        
        # 统计各种状态的任务数量
        stats = {
            "total": total,
            "pending": 0,
            "running": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0,
            "by_type": {
                "mysql_capture": 0,
                "postgres_capture": 0,
                "mongo_capture": 0
            }
        }
        
        for task in all_tasks:
            # TaskInfo对象，使用属性访问
            status = getattr(task, "status", "pending")
            task_type = getattr(task, "task_type", "unknown")

            # 统计状态
            if status in stats:
                stats[status] += 1

            # 统计类型
            if task_type in stats["by_type"]:
                stats["by_type"][task_type] += 1
        
        return stats
        
    except Exception as e:
        logger.error(f"获取任务统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务统计失败: {str(e)}")

@router.post("/ai-test-case-generation", response_model=TaskResponse)
async def submit_ai_test_case_generation_task(request: AITestCaseGenerationTaskRequest):
    """提交AI测试用例生成任务"""
    try:
        task_id = await task_management_service.submit_ai_test_case_generation_task(
            requirement=request.requirement,
            database_type=request.database_type,
            operation_type=request.operation_type,
            batch_size=request.batch_size
        )

        return TaskResponse(
            task_id=task_id,
            message=f"AI测试用例生成任务已提交，将生成 {request.batch_size} 个测试用例"
        )

    except Exception as e:
        logger.error(f"提交AI测试用例生成任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")
