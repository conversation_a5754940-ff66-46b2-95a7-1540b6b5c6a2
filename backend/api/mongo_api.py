from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import logging
import traceback
import time
import asyncio

from services.mongo_ai_service import MongoAIService
from services.mongo_service import MongoService
from services.mongo_local_packet_capture_service import MongoLocalPacketCaptureService
from utils.config import Config

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/mongo", tags=["mongodb"])

# 请求和响应模型
class MongoQueryRequest(BaseModel):
    query: str
    capture_packets: bool = True
    executor_type: str = "python"  # 执行器类型：python 或 c

class MongoQueryResponse(BaseModel):
    success: bool
    mongo_query: Optional[str] = None
    result: Optional[Any] = None
    packet_file: Optional[str] = None
    executor_type: Optional[str] = None  # 实际使用的执行器类型：python 或 c
    error: Optional[str] = None

class MongoConnectionTestResponse(BaseModel):
    success: bool
    message: str = ""
    response_time: int = 0
    databases: Optional[List[str]] = None
    collections: Optional[List[str]] = None
    error: Optional[str] = None

# 服务实例
mongo_ai_service = MongoAIService()
# 改为按需初始化，避免启动时自动连接MongoDB
mongo_service = None
packet_service = MongoLocalPacketCaptureService()

async def get_mongo_service():
    """按需获取MongoDB服务实例"""
    global mongo_service
    if mongo_service is None:
        mongo_config = Config.get_mongo_config()
        mongo_service = MongoService(**mongo_config)
        await mongo_service.initialize()
    return mongo_service

@router.post("/query", response_model=MongoQueryResponse)
async def process_mongo_query(request: MongoQueryRequest):
    """处理MongoDB自然语言查询"""
    try:
        logger.info(f"Processing MongoDB query: {request.query}, executor_type: {request.executor_type}")

        # 1. 使用AI解析自然语言为MongoDB查询
        parsed_result = await mongo_ai_service.parse_natural_language_to_mongo(request.query)
        logger.info(f"Generated MongoDB query: {parsed_result}")

        packet_file = None
        actual_executor_type = request.executor_type

        # 2. 根据选择的执行器类型初始化服务
        mongo_c_service = None
        if request.executor_type == "c":
            from services.mongo_c_executor_service import mongo_c_executor_service
            if mongo_c_executor_service.is_available():
                mongo_c_service = mongo_c_executor_service
                logger.info("Using MongoDB C executor as requested")
            else:
                logger.warning("MongoDB C executor not available, falling back to Python")
                actual_executor_type = "python"

        # 3. 如果需要抓包，启动抓包服务
        if request.capture_packets:
            packet_file = await packet_service.start_capture()
            logger.info(f"Started MongoDB packet capture: {packet_file}")
            
            # 等待抓包服务完全启动，确保能捕获到后续的MongoDB操作
            logger.info("Waiting for packet capture to fully initialize...")
            await asyncio.sleep(3)
            logger.info("Packet capture ready, proceeding with MongoDB operation...")

        # 4. 执行MongoDB查询（如果需要）
        mongo_query = parsed_result
        if not request.capture_packets:
            # 如果不需要抓包，只返回解析的查询，不执行
            logger.info("MongoDB query parsed only (not executed)")
            return MongoQueryResponse(
                success=True,
                mongo_query=mongo_query,
                result={'message': 'MongoDB query parsed successfully (not executed)', 'response_type': 'PARSE_ONLY'},
                packet_file=None,
                executor_type=actual_executor_type
            )
        else:
            # 执行MongoDB查询（使用手动连接控制以确保抓包质量）
            service = None
            try:
                if mongo_c_service and actual_executor_type == "c":
                    # 使用C语言执行器（暂时保持原有逻辑，因为C执行器可能有不同的连接管理）
                    mongo_config = Config.get_mongo_config()
                    result = await mongo_c_service.execute_single_mongo_query(
                        mongo_config['host'], mongo_config['port'],
                        mongo_config['user'], mongo_config['password'],
                        mongo_config['database'], mongo_query
                    )
                    logger.info("MongoDB query executed successfully with C executor")
                else:
                    # 使用Python执行器，采用手动连接控制
                    service = await get_mongo_service()
                    if request.capture_packets:
                        # 如果需要抓包，使用专门的抓包执行方法
                        result = await service.execute_query_for_capture(mongo_query)
                        logger.info("MongoDB query executed for capture with Python executor")

                        # 强制关闭连接以确保捕获挥手包
                        await service.force_close_connection()
                        logger.info("MongoDB connection force closed for packet capture")
                    else:
                        # 如果不需要抓包，使用常规方法
                        result = await service.execute_mongo_query(mongo_query)
                        logger.info("MongoDB query executed successfully with Python executor")
                    actual_executor_type = "python"
            except Exception as e:
                logger.error(f"MongoDB query execution failed: {str(e)}")
                # 确保连接被关闭
                if request.capture_packets and service:
                    try:
                        await service.force_close_connection()
                    except Exception as close_e:
                        logger.warning(f"Failed to force close MongoDB connection: {str(close_e)}")
                raise

        # 5. 停止抓包并获取最终的文件路径
        if request.capture_packets:
            final_packet_file = await packet_service.stop_capture()
            if final_packet_file:
                packet_file = final_packet_file
            logger.info(f"Stopped MongoDB packet capture, final file: {packet_file}")

        return MongoQueryResponse(
            success=True,
            mongo_query=mongo_query,
            result=result,
            packet_file=packet_file,
            executor_type=actual_executor_type
        )
        
    except Exception as e:
        logger.error(f"Error processing MongoDB query: {str(e)}")
        # 确保停止抓包
        if request.capture_packets:
            try:
                await packet_service.stop_capture()
            except Exception as e:

                logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
        return MongoQueryResponse(
            success=False,
            error=str(e)
        )

@router.get("/health")
async def mongo_health_check():
    """MongoDB健康检查"""
    try:
        service = await get_mongo_service()
        mongo_status = await service.check_connection()
        return {
            "status": "healthy" if mongo_status else "unhealthy",
            "mongo_connection": mongo_status,
            "packet_capture": packet_service.is_capturing_active()
        }
    except Exception as e:
        logger.error(f"MongoDB health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "mongo_connection": False,
            "packet_capture": False,
            "error": str(e)
        }

@router.post("/test-connection", response_model=MongoConnectionTestResponse)
async def test_mongo_connection():
    """测试MongoDB连接"""
    try:
        start_time = time.time()

        # 测试连接
        service = await get_mongo_service()
        is_connected = await service.check_connection()

        if not is_connected:
            return MongoConnectionTestResponse(
                success=False,
                message="MongoDB连接失败",
                error="无法连接到MongoDB服务器"
            )

        # 获取数据库列表
        databases = await service.get_databases()

        # 获取当前数据库的集合列表
        collections = await service.get_collections()

        response_time = int((time.time() - start_time) * 1000)

        return MongoConnectionTestResponse(
            success=True,
            message="MongoDB连接成功",
            response_time=response_time,
            databases=databases,
            collections=collections
        )

    except Exception as e:
        logger.error(f"MongoDB connection test failed: {str(e)}")
        return MongoConnectionTestResponse(
            success=False,
            message="MongoDB连接测试失败",
            error=str(e)
        )

@router.post("/test-c-executor")
async def test_mongo_c_executor():
    """测试MongoDB C语言执行器"""
    try:
        from services.mongo_c_executor_service import mongo_c_executor_service

        if not mongo_c_executor_service.is_available():
            return {
                "success": False,
                "message": "MongoDB C executor not available",
                "error": "C executor binary not found or not compiled"
            }

        # 测试连接
        mongo_config = Config.get_mongo_config()
        result = await mongo_c_executor_service.test_connection(
            mongo_config['host'], mongo_config['port'],
            mongo_config['user'], mongo_config['password'],
            mongo_config['database']
        )

        if result.get('success'):
            return {
                "success": True,
                "message": "MongoDB C executor test successful",
                "executor_path": mongo_c_executor_service.executor_path,
                "test_result": result
            }
        else:
            return {
                "success": False,
                "message": "MongoDB C executor test failed",
                "error": result.get('error', 'Unknown error')
            }

    except Exception as e:
        logger.error(f"MongoDB C executor test failed: {str(e)}")
        return {
            "success": False,
            "message": "MongoDB C executor test failed",
            "error": str(e)
        }

@router.get("/databases")
async def get_mongo_databases():
    """获取MongoDB数据库列表"""
    try:
        service = await get_mongo_service()
        databases = await service.get_databases()
        return {"databases": databases}
    except Exception as e:
        logger.error(f"Failed to get MongoDB databases: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/collections")
async def get_mongo_collections(database: str = None):
    """获取MongoDB集合列表"""
    try:
        service = await get_mongo_service()
        collections = await service.get_collections(database)
        return {"collections": collections}
    except Exception as e:
        logger.error(f"Failed to get MongoDB collections: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/capture/status")
async def get_mongo_capture_status():
    """获取MongoDB抓包状态"""
    return {
        "is_capturing": packet_service.is_capturing_active(),
        "current_file": packet_service.get_current_file()
    }

@router.post("/capture/start")
async def start_mongo_capture():
    """手动启动MongoDB抓包"""
    try:
        packet_file = await packet_service.start_capture()
        return {"success": True, "packet_file": packet_file}
    except Exception as e:
        logger.error(f"Failed to start MongoDB capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/capture/stop")
async def stop_mongo_capture():
    """手动停止MongoDB抓包"""
    try:
        packet_file = await packet_service.stop_capture()
        return {"success": True, "packet_file": packet_file}
    except Exception as e:
        logger.error(f"Failed to stop MongoDB capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

class MongoCaptureAnalyzeRequest(BaseModel):
    file_path: str

@router.post("/capture/analyze")
async def analyze_mongo_capture(request: MongoCaptureAnalyzeRequest):
    """分析MongoDB抓包文件"""
    try:
        analysis = await packet_service.analyze_capture_file(request.file_path)
        return {"success": True, "analysis": analysis}
    except Exception as e:
        logger.error(f"Failed to analyze MongoDB capture: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query/analyze")
async def analyze_mongo_query_intent(query: str):
    """分析MongoDB查询意图"""
    try:
        analysis = await mongo_ai_service.analyze_mongo_query_intent(query)
        return {"success": True, "analysis": analysis}
    except Exception as e:
        logger.error(f"Failed to analyze MongoDB query intent: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query/explain")
async def explain_mongo_query_result(mongo_query: str, result: Dict[str, Any]):
    """解释MongoDB查询结果"""
    try:
        explanation = await mongo_ai_service.generate_mongo_explanation(mongo_query, result)
        return {"success": True, "explanation": explanation}
    except Exception as e:
        logger.error(f"Failed to explain MongoDB query result: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query/optimize")
async def suggest_mongo_optimizations(mongo_query: str):
    """建议MongoDB查询优化"""
    try:
        suggestions = await mongo_ai_service.suggest_mongo_optimizations(mongo_query)
        return {"success": True, "suggestions": suggestions}
    except Exception as e:
        logger.error(f"Failed to suggest MongoDB optimizations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 清理服务
@router.on_event("shutdown")
async def shutdown_mongo_services():
    """关闭时清理MongoDB服务"""
    try:
        global mongo_service
        if mongo_service:
            await mongo_service.close()
        await packet_service.close()
        logger.info("MongoDB services shutdown successfully")
    except Exception as e:
        logger.error(f"Error during MongoDB services shutdown: {str(e)}")
