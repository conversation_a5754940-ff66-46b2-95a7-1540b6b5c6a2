#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
步骤校验仪表板API - 提供与任务执行对接的步骤SQL与PCAP覆盖结果
需求覆盖：
1. 展示任务执行的每个用例的步骤SQL信息（包含pcap包名称和每个步骤的SQL信息）
2. 展示PCAP包里是否包含该SQL数据（基于全新StepSQLValidationService逻辑）
3. 不沿用旧PCAP校验逻辑
"""

import os
import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException

from services.step_sql_validation_service import (
    StepSQLValidationService,
    StepValidationSummary,
)
from services.batch_execution_service import batch_execution_service
from utils.config import Config

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/step-validation-dashboard", tags=["步骤校验仪表板"])


def _extract_steps_from_execution_result(execution_result: Dict[str, Any]) -> List[Dict[str, Any]]:
    """从执行结果结构中提取步骤的SQL信息，统一为 StepSQLValidationService 所需格式。
    期望输出：[{ step_number: int, sql_statements: [str, ...] }, ...]
    """
    steps = []
    try:
        exec_steps = execution_result.get("execution_results", [])
        for idx, step in enumerate(exec_steps):
            sql_texts: List[str] = []
            # 兼容结构：sql_results: [{ sql: str, success: bool }, ...]
            for sql_item in step.get("sql_results", []):
                if isinstance(sql_item, dict):
                    sql_val = sql_item.get("sql") or sql_item.get("mongo_query") or ""
                    if isinstance(sql_val, str) and sql_val.strip():
                        sql_texts.append(sql_val)

            # 兼容结构：step 直接含 sql 或 mongo_query 字段
            direct_sql = step.get("sql") or step.get("mongo_query")
            if isinstance(direct_sql, str) and direct_sql.strip():
                sql_texts.append(direct_sql)

            if not sql_texts:
                # 确保有占位，避免后续逻辑异常
                sql_texts = []

            steps.append({
                "step_number": step.get("step_number", idx + 1),
                "sql_statements": sql_texts
            })
    except Exception as e:
        logger.error(f"提取步骤信息失败: {e}")
    return steps


def _pick_pcap_for_step(step: Dict[str, Any], fallback_files: List[str]) -> Optional[str]:
    """为当前步骤选择一个pcap文件。
    优先级：step.capture_file -> step.all_capture_files[0] -> fallback_files[0]
    """
    try:
        capture_file = step.get("capture_file")
        if isinstance(capture_file, str) and capture_file.strip():
            return capture_file
        all_files = step.get("all_capture_files") or []
        if isinstance(all_files, list) and all_files:
            return all_files[0]
    except Exception:
        pass
    if fallback_files:
        return fallback_files[0]
    return None


@router.get("/executions")
async def list_executions():
    """列出可用的（最近的）批量或单次执行任务，供前端选择查看详情。
    数据源：任务管理服务（arq_task_management_service），必要时回退内存存储（兼容）。
    返回数据包含执行任务的基本信息和统计数据。
    """
    try:
        # 1. 从数据库获取历史执行记录
        try:
            db_executions = {item['execution_id']: item for item in batch_execution_service.get_batch_executions(limit=100)}
            logger.info(f"从数据库获取到 {len(db_executions)} 条执行记录")
        except Exception as e:
            logger.error(f"从数据库获取执行记录失败: {e}")
            db_executions = {}

        # 2. 从内存获取当前（可能正在运行的）执行记录
        try:
            from api.test_case_execution_api import batch_execution_status_store
            memory_executions = {}
            for batch_id, data in batch_execution_status_store.items():
                memory_executions[batch_id] = {
                    "execution_id": batch_id,
                    "name": data.get("name", f"执行任务-{batch_id[:8]}"),
                    "database_type": data.get("database_type", "unknown"),
                    "status": str(data.get("status", "unknown")),
                    "total_cases": data.get("total_cases", 0),
                    "completed_cases": data.get("completed_cases", 0),
                    "success_cases": data.get("success_cases", 0),
                    "failed_cases": data.get("failed_cases", 0),
                    "start_time": data.get("start_time"),
                    "end_time": data.get("end_time"),
                    "duration": data.get("duration"),
                    "description": f"包含 {data.get('total_cases', 0)} 个测试用例的{data.get('database_type', 'unknown')}数据库执行任务"
                }
            logger.info(f"从内存获取到 {len(memory_executions)} 条执行记录")
        except Exception as ie:
            logger.warning(f"获取内存执行任务列表失败: {ie}")
            memory_executions = {}

        # 3. 合并结果，内存中的数据覆盖数据库中的
        merged_executions = {**db_executions, **memory_executions}
        items = list(merged_executions.values())

        # 按时间排序，最新的在前，处理None值
        items.sort(key=lambda x: x.get("start_time") or "0000-00-00T00:00:00", reverse=True)
        
        return {
            "success": True, 
            "executions": items,
            "total_count": len(items),
            "message": f"找到 {len(items)} 个执行任务" if items else "暂无执行任务记录"
        }
    except Exception as e:
        logger.error(f"列出执行任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/execution/{execution_id}")
async def get_execution_detail(execution_id: str):
    """获取指定执行（批次）的每个用例的步骤SQL与PCAP覆盖结果。
    需求覆盖：
    1. 展示任务执行的每个用例的步骤SQL信息（包含pcap包名称和每个步骤的SQL信息）
    2. 展示PCAP包里是否包含该SQL数据
    - 对每个测试用例，提取其执行步骤与关联pcap文件
    - 使用StepSQLValidationService在对应pcap内进行匹配，返回每步是否包含该SQL
    """
    try:
        # 首先尝试作为批次任务ID处理
        batch_task_data = None
        logger.info(f"开始处理执行ID: {execution_id}")
        try:
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get(f"http://localhost:8000/api/tasks/{execution_id}")
                if response.status_code == 200:
                    batch_task_data = response.json()
                    logger.info(f"找到批次任务数据: {execution_id}, 状态码: {response.status_code}")
                    logger.info(f"批次任务数据包含结果: {bool(batch_task_data.get('result', {}).get('execution_results'))}")
                else:
                    logger.info(f"批次任务API返回状态码: {response.status_code}")
        except Exception as e:
            logger.info(f"未找到批次任务数据: {e}")

        # 如果找到批次任务数据，转换为步骤校验格式
        if batch_task_data and batch_task_data.get("result", {}).get("execution_results"):
            task_result = batch_task_data["result"]
            execution_results = []
            for exec_result in task_result["execution_results"]:
                execution_results.append({
                    "test_case_id": exec_result.get("test_case_id"),
                    "test_case_title": exec_result.get("test_case_title"),
                    "capture_files": exec_result.get("capture_files", []),
                    "execution_result": {
                        "execution_results": []  # 需要从单个测试用例获取详细步骤
                    }
                })
            result_data = {"execution_results": execution_results}
            logger.info(f"转换批次任务为步骤校验格式，包含 {len(execution_results)} 个测试用例")
            
            # 直接跳转到处理逻辑，不再尝试其他数据源
            execution_results_list = result_data.get("execution_results", [])
            if not execution_results_list:
                return {"success": True, "execution_id": execution_id, "cases": []}
                
            validator = StepSQLValidationService()
            cases_output = []
            
            for case in execution_results_list:
                # 处理批次任务数据的步骤校验逻辑
                test_case_id = case.get("test_case_id")
                test_case_title = case.get("test_case_title")
                capture_files = case.get("capture_files") or []
                
                logger.info(f"处理批次任务测试用例: {test_case_id}, PCAP文件: {len(capture_files)} 个")
                
                # 从单个测试用例获取详细步骤信息
                try:
                    import httpx
                    async with httpx.AsyncClient() as client:
                        response = await client.get(f"http://localhost:8000/api/test-cases/{test_case_id}/last-execution")
                        if response.status_code == 200:
                            single_case_data = response.json()
                            execution_details = single_case_data.get("execution_details", {})
                            detailed_steps = execution_details.get("execution_results", [])
                        else:
                            detailed_steps = []
                except Exception as e:
                    logger.warning(f"获取测试用例详细步骤失败: {e}")
                    detailed_steps = []
                
                # 处理每个步骤的校验
                step_outputs = []
                for idx, step in enumerate(detailed_steps):
                    step_meta = {
                        "step_number": step.get("step_number", idx + 1),
                        "pcap_file": None,
                        "sqls": [],
                        "validation": None,
                    }
                    
                    # 收集SQL语句
                    sql_texts = []
                    for sql_item in step.get("sql_results", []) or []:
                        if isinstance(sql_item, dict):
                            sql_val = sql_item.get("sql") or sql_item.get("mongo_query") or ""
                            if isinstance(sql_val, str) and sql_val.strip():
                                sql_texts.append(sql_val)
                    
                    step_meta["sqls"] = sql_texts
                    
                    # 选择PCAP文件进行校验
                    chosen_pcap = capture_files[0] if capture_files else None
                    step_meta["pcap_file"] = chosen_pcap
                    
                    if chosen_pcap and sql_texts:
                        from utils.config import Config
                        pcap_path = chosen_pcap
                        if not os.path.isabs(pcap_path):
                            pcap_path = os.path.join(Config.CAPTURE_DIR, chosen_pcap)
                        
                        if os.path.exists(pcap_path):
                            from services.step_sql_validation_service import StepValidationSummary
                            summary = validator.validate_steps_with_pcap(
                                execution_steps=[{"step_number": step_meta["step_number"], "sql_statements": sql_texts}],
                                pcap_file=pcap_path,
                            )
                            if summary.step_results:
                                r = summary.step_results[0]
                                step_meta["validation"] = {
                                    "status": r.validation_status.value,
                                    "similarity": r.similarity_score,
                                    "confidence": r.confidence,
                                    "matched": r.validation_status.value in ["matched", "partial", "similar"],
                                    "matched_statements_count": len(r.matched_statements),
                                    "packet_numbers": r.packet_numbers,
                                    "error_message": r.error_message
                                }
                        else:
                            step_meta["validation"] = {
                                "status": "pcap_missing",
                                "matched": False,
                                "reason": f"PCAP不存在: {pcap_path}"
                            }
                    else:
                        step_meta["validation"] = {
                            "status": "pcap_missing",
                            "matched": False,
                            "reason": "未找到PCAP文件或SQL语句"
                        }
                    
                    step_outputs.append(step_meta)
                
                # 计算统计信息
                total_steps = len(step_outputs)
                matched_steps = sum(1 for s in step_outputs if s.get("validation", {}).get("matched", False))
                success_rate = matched_steps / total_steps if total_steps > 0 else 0.0
                
                cases_output.append({
                    "test_case_id": test_case_id,
                    "test_case_title": test_case_title,
                    "capture_files": capture_files,
                    "steps": step_outputs,
                    "statistics": {
                        "total_steps": total_steps,
                        "matched_steps": matched_steps,
                        "success_rate": success_rate,
                        "validation_summary": {
                            "matched": sum(1 for s in step_outputs if s.get("validation", {}).get("status") == "matched"),
                            "partial": sum(1 for s in step_outputs if s.get("validation", {}).get("status") == "partial"),
                            "similar": sum(1 for s in step_outputs if s.get("validation", {}).get("status") == "similar"),
                            "not_found": sum(1 for s in step_outputs if s.get("validation", {}).get("status") == "not_found"),
                            "pcap_missing": sum(1 for s in step_outputs if s.get("validation", {}).get("status") == "pcap_missing")
                        }
                    }
                })
            
            # 计算整体统计信息
            total_cases = len(cases_output)
            total_all_steps = sum(case.get("statistics", {}).get("total_steps", 0) for case in cases_output)
            total_matched_steps = sum(case.get("statistics", {}).get("matched_steps", 0) for case in cases_output)
            overall_success_rate = total_matched_steps / total_all_steps if total_all_steps > 0 else 0.0
            
            return {
                "success": True, 
                "execution_id": execution_id, 
                "cases": cases_output,
                "overall_statistics": {
                    "total_cases": total_cases,
                    "total_steps": total_all_steps,
                    "matched_steps": total_matched_steps,
                    "overall_success_rate": overall_success_rate
                }
            }
        else:
            # 尝试作为单个测试用例执行ID处理
            single_case_data = None
            try:
                import httpx
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"http://localhost:8000/api/test-cases/{execution_id}/last-execution")
                    if response.status_code == 200:
                        single_case_data = response.json()
                        logger.info(f"找到单个测试用例执行数据: {execution_id}")
            except Exception as e:
                logger.info(f"未找到单个测试用例执行数据: {e}")

            # 如果找到单个测试用例数据，转换为批次格式
            if single_case_data and single_case_data.get("execution_details"):
                execution_details = single_case_data["execution_details"]
                result_data = {
                    "execution_results": [{
                        "test_case_id": execution_id,
                        "test_case_title": f"测试用例 {execution_id[:8]}",
                        "capture_files": single_case_data.get("capture_files", []),
                        "execution_result": {
                            "execution_results": execution_details.get("execution_results", [])
                        }
                    }]
                }
                logger.info(f"转换单个测试用例为批次格式，包含 {len(execution_details.get('execution_results', []))} 个步骤")
            else:
                # 回退到批次执行处理
                task_info = None
                try:
                    from services.arq_task_management_service import arq_task_management_service as task_management_service
                    task_info = task_management_service.get_task_info(execution_id)
                    if task_info:
                        # 从完整的任务结果中提取执行数据
                        task_result = task_info.get("result", {})
                        if task_result and task_result.get("execution_results"):
                            # 转换任务结果为步骤校验格式
                            execution_results = []
                            for exec_result in task_result["execution_results"]:
                                execution_results.append({
                                    "test_case_id": exec_result.get("test_case_id"),
                                    "test_case_title": exec_result.get("test_case_title"),
                                    "capture_files": exec_result.get("capture_files", []),
                                    "execution_result": {
                                        "execution_results": []  # 需要从单个测试用例获取详细步骤
                                    }
                                })
                            execution_result = {"execution_results": execution_results}
                            logger.info(f"从任务管理服务获取到批次执行结果，包含 {len(execution_results)} 个测试用例")
                        else:
                            execution_result = task_info.get("execution_result", {})
                            logger.info("从任务管理服务获取到执行结果")
                    else:
                        execution_result = {}
                except Exception as e:
                    logger.warning(f"任务管理服务获取失败: {e}")
                    execution_result = {}

            if not execution_result:
                # 回退到内存存储
                try:
                    from api.test_case_execution_api import batch_execution_status_store
                    if execution_id not in batch_execution_status_store:
                        raise HTTPException(status_code=404, detail="执行任务不存在")
                    batch_data = batch_execution_status_store[execution_id]
                    test_case_items = batch_data.get("test_case_items", [])
                    
                    # 为每个测试用例添加capture_files信息
                    for item in test_case_items:
                        if not item.get("capture_files"):
                            # 从execution_result中提取capture_files
                            exec_result = item.get("execution_result", {})
                            if exec_result.get("capture_files"):
                                item["capture_files"] = exec_result["capture_files"]
                            elif exec_result.get("summary", {}).get("capture_files"):
                                item["capture_files"] = exec_result["summary"]["capture_files"]
                            else:
                                item["capture_files"] = []
                    
                    result_data = {
                        "execution_results": test_case_items
                    }
                    logger.info(f"从内存存储获取到 {len(result_data['execution_results'])} 个测试用例")
                except Exception as ie:
                    logger.warning(f"读取内存执行结果失败: {ie}")
                    return {"success": True, "execution_id": execution_id, "cases": []}
            else:
                # 从任务管理服务获取的结果
                result_data = {"execution_results": execution_result.get("execution_results", [])}
                logger.info(f"从任务管理服务获取到 {len(result_data['execution_results'])} 个测试用例")

        execution_results: List[Dict[str, Any]] = result_data.get("execution_results", [])
        if not execution_results:
            return {"success": True, "execution_id": execution_id, "cases": []}

        validator = StepSQLValidationService()
        cases_output: List[Dict[str, Any]] = []

        for case in execution_results:
            test_case_id = case.get("test_case_id")
            test_case_title = case.get("test_case_title")
            capture_files: List[str] = case.get("capture_files") or []
            execution_result = case.get("execution_result") or {}
            
            logger.info(f"处理测试用例: {test_case_id}, 标题: {test_case_title}")
            logger.info(f"capture_files: {capture_files}")
            logger.info(f"execution_result keys: {list(execution_result.keys()) if execution_result else 'None'}")

            # 提取步骤：优先从execution_result，其次从test_case_json中的test_steps
            steps = _extract_steps_from_execution_result(execution_result)
            logger.info(f"从execution_result提取到 {len(steps)} 个步骤")
            if not steps:
                try:
                    # 首先尝试从test_case_json获取
                    raw_json = case.get("test_case_json")
                    logger.info(f"test_case_json存在: {raw_json is not None}, 类型: {type(raw_json)}")
                    
                    test_steps = []
                    if isinstance(raw_json, str) and raw_json.strip() and raw_json != '{}':
                        import json as _json
                        tc = _json.loads(raw_json)
                        test_steps = tc.get("test_steps", [])
                        logger.info(f"从test_case_json解析到 {len(test_steps)} 个test_steps")
                    
                    # 如果test_case_json为空或None，直接从测试用例API获取原始数据
                    logger.info(f"test_steps数量检查: {len(test_steps)}")
                    if not test_steps:
                        logger.info(f"test_case_json为空，从API获取测试用例原始数据: {test_case_id}")
                        import httpx
                        try:
                            async with httpx.AsyncClient() as client:
                                response = await client.get(f"http://localhost:8000/api/test-cases/{test_case_id}")
                                logger.info(f"API响应状态码: {response.status_code}")
                                if response.status_code == 200:
                                    test_case_data = response.json()
                                    test_steps = test_case_data.get("test_steps", [])
                                    logger.info(f"从API获取到 {len(test_steps)} 个test_steps")
                                else:
                                    logger.warning(f"API调用失败，状态码: {response.status_code}")
                        except Exception as api_e:
                            logger.error(f"API调用异常: {api_e}")
                    else:
                        logger.info(f"已有test_steps，跳过API调用")
                    
                    steps = []
                    for idx, st in enumerate(test_steps):
                        sql_texts: List[str] = []
                        
                        # 检查sql_statement字段
                        if isinstance(st.get("sql_statement"), str) and st["sql_statement"].strip():
                            sql_texts.append(st["sql_statement"].strip())
                        
                        # 检查test_data字段，这里通常包含实际的SQL语句
                        td = st.get("test_data")
                        if isinstance(td, str) and td.strip():
                            # 按分号分割SQL语句，过滤空语句
                            parts = [p.strip() for p in td.split(';') if p.strip()]
                            sql_texts.extend(parts)
                        
                        # 即使没有SQL语句也要创建步骤记录
                        steps.append({
                            "step_number": st.get("step_number", idx + 1),
                            "sql_statements": sql_texts
                        })
                        
                    logger.info(f"提取到 {len(steps)} 个步骤")
                except Exception as ie:
                    logger.warning(f"从test_case_json提取步骤失败: {ie}")

            # 针对每个步骤选择pcap并做校验
            step_outputs: List[Dict[str, Any]] = []
            detailed_steps = execution_result.get("execution_results") if execution_result else []
            if not isinstance(detailed_steps, list) or not detailed_steps:
                detailed_steps = steps

            for idx, step in enumerate(detailed_steps):
                step_meta = {
                    "step_number": step.get("step_number", idx + 1),
                    "pcap_file": None,
                    "sqls": [],
                    "validation": None,
                }

                # SQL收集展示
                sql_texts = []
                for sql_item in step.get("sql_results", []) or []:
                    if isinstance(sql_item, dict):
                        sql_val = sql_item.get("sql") or sql_item.get("mongo_query") or ""
                        if isinstance(sql_val, str) and sql_val.strip():
                            sql_texts.append(sql_val)
                if not sql_texts:
                    direct_sql = step.get("sql") or step.get("mongo_query")
                    if isinstance(direct_sql, str) and direct_sql.strip():
                        sql_texts.append(direct_sql)
                if not sql_texts and isinstance(step.get("sql_statements"), list):
                    sql_texts = [s for s in step.get("sql_statements") if isinstance(s, str) and s.strip()]

                step_meta["sqls"] = sql_texts

                chosen_pcap = _pick_pcap_for_step(step, capture_files)
                step_meta["pcap_file"] = chosen_pcap

                # 如果有可用pcap，则执行步骤校验
                if chosen_pcap:
                    pcap_path = chosen_pcap
                    if not os.path.isabs(pcap_path):
                        pcap_path = os.path.join(Config.CAPTURE_DIR, chosen_pcap)
                    if os.path.exists(pcap_path):
                        summary: StepValidationSummary = validator.validate_steps_with_pcap(
                            execution_steps=[{"step_number": step_meta["step_number"], "sql_statements": sql_texts}],
                            pcap_file=pcap_path,
                        )
                        if summary.step_results:
                            r = summary.step_results[0]
                            step_meta["validation"] = {
                                "status": r.validation_status.value,
                                "similarity": r.similarity_score,
                                "confidence": r.confidence,
                                "matched": r.validation_status.value in ["matched", "partial", "similar"],
                                "matched_statements_count": len(r.matched_statements),
                                "matched_statements": [
                                    {
                                        "sql": s.sql,
                                        "sql_type": s.sql_type.value,
                                        "database_type": s.database_type.value,
                                        "packet_number": s.packet_number,
                                        "timestamp": s.timestamp,
                                        "confidence": s.confidence
                                    }
                                    for s in r.matched_statements
                                ],
                                "packet_numbers": r.packet_numbers,
                                "error_message": r.error_message
                            }
                            try:
                                step_meta["pcap_file"] = os.path.basename(pcap_path)
                                step_meta["pcap_full_path"] = pcap_path
                            except Exception:
                                pass
                    else:
                        step_meta["validation"] = {
                            "status": "pcap_missing",
                            "matched": False,
                            "reason": f"PCAP不存在: {pcap_path}"
                        }
                else:
                    step_meta["validation"] = {
                        "status": "pcap_missing",
                        "matched": False,
                        "reason": "未找到PCAP文件或文件不存在"
                    }

                step_outputs.append(step_meta)

            # 计算用例级别的统计信息
            total_steps = len(step_outputs)
            matched_steps = sum(1 for s in step_outputs if s.get("validation", {}).get("matched", False))
            success_rate = matched_steps / total_steps if total_steps > 0 else 0.0
            
            cases_output.append({
                "test_case_id": test_case_id,
                "test_case_title": test_case_title,
                "capture_files": capture_files,
                "steps": step_outputs,
                "statistics": {
                    "total_steps": total_steps,
                    "matched_steps": matched_steps,
                    "success_rate": success_rate,
                    "validation_summary": {
                        "matched": sum(1 for s in step_outputs if s.get("validation", {}).get("status") == "matched"),
                        "partial": sum(1 for s in step_outputs if s.get("validation", {}).get("status") == "partial"),
                        "similar": sum(1 for s in step_outputs if s.get("validation", {}).get("status") == "similar"),
                        "not_found": sum(1 for s in step_outputs if s.get("validation", {}).get("status") == "not_found"),
                        "pcap_missing": sum(1 for s in step_outputs if s.get("validation", {}).get("status") == "pcap_missing")
                    }
                }
            })

        # 计算整体统计信息
        total_cases = len(cases_output)
        total_all_steps = sum(case.get("statistics", {}).get("total_steps", 0) for case in cases_output)
        total_matched_steps = sum(case.get("statistics", {}).get("matched_steps", 0) for case in cases_output)
        overall_success_rate = total_matched_steps / total_all_steps if total_all_steps > 0 else 0.0
        
        return {
            "success": True, 
            "execution_id": execution_id, 
            "cases": cases_output,
            "overall_statistics": {
                "total_cases": total_cases,
                "total_steps": total_all_steps,
                "matched_steps": total_matched_steps,
                "overall_success_rate": overall_success_rate
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/execution/{execution_id}/detailed-steps")
async def get_execution_detailed_steps(execution_id: str):
    """获取指定执行任务的详细步骤SQL信息和PCAP包匹配结果。
    专门用于展示每个步骤的SQL语句和对应的PCAP包中的匹配情况。
    """
    try:
        # 获取执行详情
        execution_detail = await get_execution_detail(execution_id)
        if not execution_detail.get("success"):
            raise HTTPException(status_code=404, detail="执行任务不存在")
        
        detailed_steps = []
        
        for case in execution_detail.get("cases", []):
            case_steps = {
                "test_case_id": case.get("test_case_id"),
                "test_case_title": case.get("test_case_title"),
                "capture_files": case.get("capture_files", []),
                "statistics": case.get("statistics", {}),
                "steps": []
            }
            
            for step in case.get("steps", []):
                step_detail = {
                    "step_number": step.get("step_number"),
                    "pcap_file": step.get("pcap_file"),
                    "pcap_full_path": step.get("pcap_full_path"),
                    "sql_statements": {
                        "count": len(step.get("sqls", [])),
                        "statements": [
                            {
                                "index": idx + 1,
                                "sql": sql,
                                "sql_type": "unknown",  # 可以根据需要添加SQL类型分析
                                "length": len(sql),
                                "preview": sql[:100] + "..." if len(sql) > 100 else sql
                            }
                            for idx, sql in enumerate(step.get("sqls", []))
                        ]
                    },
                    "pcap_validation": {
                        "status": step.get("validation", {}).get("status", "unknown"),
                        "matched": step.get("validation", {}).get("matched", False),
                        "similarity": step.get("validation", {}).get("similarity", 0.0),
                        "confidence": step.get("validation", {}).get("confidence", 0.0),
                        "matched_statements_count": step.get("validation", {}).get("matched_statements_count", 0),
                        "packet_numbers": step.get("validation", {}).get("packet_numbers", []),
                        "error_message": step.get("validation", {}).get("error_message"),
                        "reason": step.get("validation", {}).get("reason")
                    },
                    "pcap_matched_statements": [
                        {
                            "sql": stmt.get("sql"),
                            "sql_type": stmt.get("sql_type"),
                            "database_type": stmt.get("database_type"),
                            "packet_number": stmt.get("packet_number"),
                            "timestamp": stmt.get("timestamp"),
                            "confidence": stmt.get("confidence"),
                            "preview": stmt.get("sql", "")[:100] + "..." if len(stmt.get("sql", "")) > 100 else stmt.get("sql", "")
                        }
                        for stmt in step.get("validation", {}).get("matched_statements", [])
                    ]
                }
                case_steps["steps"].append(step_detail)
            
            detailed_steps.append(case_steps)
        
        return {
            "success": True,
            "execution_id": execution_id,
            "overall_statistics": execution_detail.get("overall_statistics", {}),
            "detailed_steps": detailed_steps,
            "summary": {
                "total_cases": len(detailed_steps),
                "total_steps": sum(len(case["steps"]) for case in detailed_steps),
                "total_sql_statements": sum(
                    sum(step["sql_statements"]["count"] for step in case["steps"])
                    for case in detailed_steps
                ),
                "total_matched_pcap_statements": sum(
                    sum(len(step["pcap_matched_statements"]) for step in case["steps"])
                    for case in detailed_steps
                )
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取详细步骤信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/step/{execution_id}/{case_id}/{step_number}")
async def get_step_detail(execution_id: str, case_id: str, step_number: int):
    """获取单个步骤的详细信息，包括SQL语句和PCAP包匹配结果。"""
    try:
        execution_detail = await get_execution_detail(execution_id)
        if not execution_detail.get("success"):
            raise HTTPException(status_code=404, detail="执行任务不存在")
        
        # 查找指定的测试用例和步骤
        target_case = None
        target_step = None
        
        for case in execution_detail.get("cases", []):
            if case.get("test_case_id") == case_id:
                target_case = case
                for step in case.get("steps", []):
                    if step.get("step_number") == step_number:
                        target_step = step
                        break
                break
        
        if not target_case:
            raise HTTPException(status_code=404, detail=f"测试用例 {case_id} 不存在")
        
        if not target_step:
            raise HTTPException(status_code=404, detail=f"步骤 {step_number} 不存在")
        
        return {
            "success": True,
            "execution_id": execution_id,
            "test_case_id": case_id,
            "test_case_title": target_case.get("test_case_title"),
            "step_number": step_number,
            "step_detail": {
                "pcap_file": target_step.get("pcap_file"),
                "pcap_full_path": target_step.get("pcap_full_path"),
                "sql_statements": target_step.get("sqls", []),
                "validation": target_step.get("validation", {}),
                "matched_statements": target_step.get("validation", {}).get("matched_statements", [])
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取步骤详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


