"""
GaussDB数据库服务 V2
支持华为GaussDB数据库的连接和操作，优先使用专用驱动，回退到PostgreSQL协议，支持连接池
"""

import asyncio
from typing import List, Dict, Any, Optional
import logging
import traceback
from contextlib import asynccontextmanager
import threading
import time

logger = logging.getLogger(__name__)

# 导入C语言执行器服务
try:
    from services.gaussdb_c_executor_service import gaussdb_c_executor_service
    C_EXECUTOR_AVAILABLE = True
    logger.info("GaussDB C executor service is available")
except ImportError as e:
    C_EXECUTOR_AVAILABLE = False
    logger.warning(f"GaussDB C executor service not available: {e}")

# 尝试导入GaussDB专用驱动
GAUSSDB_DRIVER_AVAILABLE = False
gaussdb_module = None

# 临时禁用GaussDB专用驱动以避免段错误
# try:
#     # 尝试导入PyGaussDB
#     import PyGaussDB as gaussdb_module
#     GAUSSDB_DRIVER_AVAILABLE = True
#     DRIVER_TYPE = "PyGaussDB"
#     logger.info("PyGaussDB driver is available")
# except ImportError:
# 完全禁用所有GaussDB专用驱动，强制使用PostgreSQL驱动
# if False:  # 禁用GaussDB专用驱动
#     pass
# else:
#     try:
#         # 尝试导入gaussdb
#         import gaussdb as gaussdb_module
#         GAUSSDB_DRIVER_AVAILABLE = True
#         DRIVER_TYPE = "gaussdb"
#         logger.info("GaussDB driver is available")
#     except ImportError:
#         try:
#             # 尝试导入gaussdb-python-driver
#             from gaussdb_python_driver import connect as gaussdb_connect
#             gaussdb_module = type('GaussDBModule', (), {'connect': gaussdb_connect})()
#             GAUSSDB_DRIVER_AVAILABLE = True
#             DRIVER_TYPE = "gaussdb-python-driver"
#             logger.info("gaussdb-python-driver is available")
#         except ImportError:
#             # 回退到PostgreSQL驱动
#             import psycopg2
#             import psycopg2.extras
#             GAUSSDB_DRIVER_AVAILABLE = False
#             DRIVER_TYPE = "psycopg2"
#             logger.warning("GaussDB specific driver not found, falling back to PostgreSQL driver")

# 强制使用PostgreSQL驱动
import psycopg2
import psycopg2.extras
GAUSSDB_DRIVER_AVAILABLE = False
gaussdb_module = None  # 确保gaussdb_module为None
DRIVER_TYPE = "psycopg2"
logger.info("强制使用PostgreSQL驱动连接GaussDB，避免段错误")

class GaussDBServiceV2:
    """GaussDB数据库服务 V2 - 支持多种驱动和连接池"""

    def __init__(self, host: str, port: int, user: str, password: str, database: str,
                 use_c_executor: bool = False):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
        self._persistent_connection = None
        self.driver_type = DRIVER_TYPE

        # C语言执行器选项
        self.use_c_executor = use_c_executor
        self._c_executor_service = None

        # 连接池配置
        self.pool = None
        self.pool_lock = threading.Lock()
        self.pool_min_size = 2
        self.pool_max_size = 10
        self.pool_increment = 1

    async def initialize(self):
        """初始化服务"""
        # 如果使用C语言执行器
        if self.use_c_executor and C_EXECUTOR_AVAILABLE:
            self._c_executor_service = gaussdb_c_executor_service
            if not self._c_executor_service.is_available():
                logger.warning("C executor not available, falling back to Python execution")
                self.use_c_executor = False
            else:
                logger.info("GaussDB service V2 initialized with C executor")
                return

        # 使用Python执行器
        try:
            # 尝试创建连接池
            await self._create_connection_pool()
            logger.info(f"GaussDB service V2 initialized successfully using {self.driver_type} with connection pool")
        except Exception as e:
            logger.warning(f"Failed to create connection pool, falling back to single connection: {e}")
            # 回退到单连接模式
            await self.check_connection()
            logger.info(f"GaussDB service V2 initialized with single connection using {self.driver_type}")

    async def _create_connection_pool(self):
        """创建GaussDB连接池"""
        try:
            with self.pool_lock:
                if self.pool is not None:
                    return self.pool

                logger.info(f"Creating GaussDB connection pool: {self.host}:{self.port}/{self.database}")

                # 对于PostgreSQL驱动，使用psycopg2的连接池
                if DRIVER_TYPE == "psycopg2":
                    import psycopg2.pool

                    self.pool = psycopg2.pool.ThreadedConnectionPool(
                        minconn=self.pool_min_size,
                        maxconn=self.pool_max_size,
                        host=self.host,
                        port=self.port,
                        user=self.user,
                        password=self.password,
                        database=self.database,
                        connect_timeout=30,
                        sslmode='disable'
                    )

                    # 测试连接池
                    test_conn = self.pool.getconn()
                    self.pool.putconn(test_conn)

                    logger.info(f"GaussDB PostgreSQL connection pool created successfully")
                    logger.info(f"Pool config: min={self.pool_min_size}, max={self.pool_max_size}")
                    return self.pool
                else:
                    # 对于GaussDB专用驱动，暂时不支持连接池，使用单连接
                    logger.warning(f"Connection pool not supported for {DRIVER_TYPE}, using single connection")
                    raise Exception(f"Connection pool not supported for {DRIVER_TYPE}")

        except Exception as e:
            logger.error(f"Failed to create GaussDB connection pool: {str(e)}")
            raise

    def _get_pool_connection(self):
        """从连接池获取连接"""
        try:
            if self.pool is None:
                raise Exception("Connection pool not available")

            if DRIVER_TYPE == "psycopg2":
                connection = self.pool.getconn()
                logger.debug("Acquired connection from GaussDB pool")
                return connection
            else:
                raise Exception(f"Pool connection not supported for {DRIVER_TYPE}")

        except Exception as e:
            logger.error(f"Failed to get connection from GaussDB pool: {str(e)}")
            raise

    def _return_pool_connection(self, connection):
        """将连接返回到连接池"""
        try:
            if self.pool and connection:
                if DRIVER_TYPE == "psycopg2":
                    self.pool.putconn(connection)
                    logger.debug("Returned connection to GaussDB pool")

        except Exception as e:
            logger.error(f"Failed to return connection to GaussDB pool: {str(e)}")

    def close_pool(self):
        """关闭连接池"""
        try:
            with self.pool_lock:
                if self.pool is not None:
                    if DRIVER_TYPE == "psycopg2":
                        self.pool.closeall()
                    self.pool = None
                    logger.info("GaussDB connection pool closed")
        except Exception as e:
            logger.error(f"Error closing GaussDB connection pool: {str(e)}")
    
    def _create_connection_gaussdb(self):
        """使用GaussDB专用驱动创建连接"""
        if DRIVER_TYPE == "PyGaussDB":
            return gaussdb_module.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                timeout=30
            )
        elif DRIVER_TYPE == "gaussdb":
            return gaussdb_module.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database
            )
        elif DRIVER_TYPE == "gaussdb-python-driver":
            return gaussdb_module.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database
            )
        else:
            raise Exception("No GaussDB driver available")
    
    def _create_connection_postgresql(self):
        """使用PostgreSQL驱动创建连接"""
        connection_attempts = [
            # 尝试1: 标准连接
            lambda: psycopg2.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                connect_timeout=30,
                options='-c search_path=public'
            ),
            # 尝试2: 不使用options
            lambda: psycopg2.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                connect_timeout=30
            ),
            # 尝试3: 禁用SSL（GaussDB常用）
            lambda: psycopg2.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                connect_timeout=30,
                sslmode='disable'
            ),
            # 尝试4: 允许SSL但不要求
            lambda: psycopg2.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                connect_timeout=30,
                sslmode='allow'
            ),
            # 尝试5: 偏好SSL但不要求
            lambda: psycopg2.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                connect_timeout=30,
                sslmode='prefer'
            )
        ]
        
        last_error = None
        for i, attempt in enumerate(connection_attempts, 1):
            try:
                logger.info(f"PostgreSQL connection attempt {i}")
                return attempt()
            except Exception as e:
                last_error = e
                logger.warning(f"PostgreSQL connection attempt {i} failed: {e}")
        
        raise last_error
    
    def _create_connection(self):
        """创建数据库连接"""
        if GAUSSDB_DRIVER_AVAILABLE and gaussdb_module:
            try:
                logger.info(f"Using GaussDB specific driver: {DRIVER_TYPE}")
                return self._create_connection_gaussdb()
            except Exception as e:
                logger.warning(f"GaussDB driver failed: {e}, falling back to PostgreSQL driver")
        
        # 回退到PostgreSQL驱动
        logger.info("Using PostgreSQL driver for GaussDB connection")
        return self._create_connection_postgresql()
    
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        connection = None
        try:
            # 添加超时机制，防止连接创建无限等待
            connection = await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(
                    None, self._create_connection
                ),
                timeout=10.0  # 10秒超时
            )
            yield connection
        except asyncio.TimeoutError:
            logger.error("GaussDB connection creation timeout (10 seconds)")
            raise Exception("GaussDB connection timeout")
        except Exception as e:
            logger.error(f"GaussDB connection error: {str(e)}")
            raise
        finally:
            if connection:
                try:
                    connection.close()
                except Exception as e:

                    logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                    logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
    async def check_connection(self) -> bool:
        """检查数据库连接"""
        try:
            async with self.get_connection() as conn:
                if GAUSSDB_DRIVER_AVAILABLE and gaussdb_module:
                    # GaussDB专用驱动的测试查询
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT version()")
                        version = cursor.fetchone()[0]
                        logger.info(f"GaussDB version: {version}")
                else:
                    # PostgreSQL驱动的测试查询
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT version()")
                        version = cursor.fetchone()[0]
                        logger.info(f"GaussDB (via PostgreSQL) version: {version}")
            return True
        except Exception as e:
            logger.error(f"GaussDB connection check failed: {str(e)}")
            return False

    async def execute_sql_query(self, sql_query: str) -> Dict[str, Any]:
        """执行GaussDB SQL查询 - 使用连接池"""
        try:
            logger.info(f"Executing GaussDB query: {sql_query}")

            # 在线程池中执行查询
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._execute_query_sync, sql_query)

            logger.info(f"GaussDB query executed successfully")
            return result

        except Exception as e:
            logger.error(f"Failed to execute GaussDB query: {str(e)}")
            raise Exception(f"GaussDB query execution failed: {str(e)}")

    def _execute_query_sync(self, sql_query: str) -> Dict[str, Any]:
        """同步执行SQL查询 - 使用连接池"""
        conn = None
        use_pool = False
        try:
            # 优先使用连接池
            if self.pool:
                conn = self._get_pool_connection()
                use_pool = True
            else:
                conn = self._create_connection()
                use_pool = False

            try:
                # 设置查询超时（防止长时间运行的查询卡住）
                timeout_seconds = 300  # 默认5分钟

                # 对于大数据量插入，增加超时时间
                if 'INSERT' in sql_query.upper() and 'generate_series' in sql_query.lower():
                    logger.info("检测到大数据量插入操作，设置更长的超时时间")
                    timeout_seconds = 600  # 10分钟

                if GAUSSDB_DRIVER_AVAILABLE and gaussdb_module:
                    # 使用GaussDB专用驱动
                    with conn.cursor() as cursor:
                        # 设置语句超时
                        cursor.execute(f"SET statement_timeout = '{timeout_seconds}s'")
                        conn.commit()

                        cursor.execute(sql_query.strip().rstrip(';'))

                        query_upper = sql_query.upper().strip()
                        if query_upper.startswith('SELECT') or query_upper.startswith('WITH'):
                            rows = cursor.fetchall()
                            # 转换为字典列表
                            columns = [desc[0] for desc in cursor.description] if cursor.description else []
                            data = [dict(zip(columns, row)) for row in rows]

                            return {
                                'type': 'query',
                                'data': data,
                                'count': len(data),
                                'columns': columns
                            }
                        else:
                            conn.commit()
                            return {
                                'type': 'modification',
                                'message': 'Command executed successfully'
                            }
                else:
                    # 使用PostgreSQL驱动
                    import psycopg2.extras
                    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                        # 设置语句超时
                        cursor.execute(f"SET statement_timeout = '{timeout_seconds}s'")
                        conn.commit()

                        query = sql_query.strip().rstrip(';')
                        cursor.execute(query)

                        query_upper = query.upper().strip()
                        if query_upper.startswith('SELECT') or query_upper.startswith('WITH'):
                            rows = cursor.fetchall()
                            data = [dict(row) for row in rows]

                            return {
                                'type': 'query',
                                'data': data,
                                'count': len(data),
                                'columns': [desc[0] for desc in cursor.description] if cursor.description else []
                            }
                        else:
                            conn.commit()
                            affected_rows = cursor.rowcount
                            return {
                                'type': 'modification',
                                'affected_rows': affected_rows,
                                'message': f'Command executed successfully, {affected_rows} rows affected'
                            }

            finally:
                if conn:
                    if use_pool:
                        # 将连接返回到连接池
                        self._return_pool_connection(conn)
                    else:
                        # 关闭单连接
                        conn.close()
                
        except Exception as e:
            logger.error(f"GaussDB query execution error: {str(e)}")
            raise

    async def get_databases(self) -> List[str]:
        """获取数据库列表"""
        try:
            result = await self.execute_sql_query(
                "SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname"
            )
            return [row['datname'] for row in result['data']]
        except Exception as e:
            logger.error(f"Failed to get GaussDB databases: {str(e)}")
            raise

    async def get_tables(self, schema: str = 'public') -> List[str]:
        """获取表列表"""
        try:
            result = await self.execute_sql_query(
                f"SELECT tablename FROM pg_tables WHERE schemaname = '{schema}' ORDER BY tablename"
            )
            return [row['tablename'] for row in result['data']]
        except Exception as e:
            logger.error(f"Failed to get GaussDB tables: {str(e)}")
            raise

    async def test_connection_with_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """使用指定配置测试连接"""
        try:
            # 创建临时服务实例
            temp_service = GaussDBServiceV2(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database']
            )
            
            # 测试连接
            is_connected = await temp_service.check_connection()
            if is_connected:
                return {'success': True, 'driver': temp_service.driver_type}
            else:
                return {'success': False, 'error': 'Connection failed'}
            
        except Exception as e:
            logger.error(f"GaussDB connection test failed: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def create_persistent_connection(self) -> bool:
        """创建持久连接（用于抓包场景）"""
        try:
            # 如果使用C语言执行器
            if self.use_c_executor and self._c_executor_service:
                logger.info("Creating persistent GaussDB connection using C executor")
                success = await self._c_executor_service.create_persistent_connection(
                    self.host, self.port, self.user, self.password, self.database
                )
                if success:
                    logger.info("Persistent GaussDB C executor connection created successfully")
                    return True
                else:
                    logger.error("Failed to create persistent GaussDB C executor connection")
                    return False

            # 使用Python执行器
            if self._persistent_connection:
                logger.info("Persistent connection already exists")
                return True

            logger.info("Creating persistent GaussDB connection")
            self._persistent_connection = await asyncio.get_event_loop().run_in_executor(
                None, self._create_connection
            )

            # 测试连接
            if GAUSSDB_DRIVER_AVAILABLE and gaussdb_module:
                with self._persistent_connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
            else:
                with self._persistent_connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()

            logger.info("Persistent GaussDB connection created successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to create persistent GaussDB connection: {str(e)}")
            if self._persistent_connection:
                try:
                    self._persistent_connection.close()
                except Exception as e:

                    logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                    logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                self._persistent_connection = None
            return False

    async def create_fresh_connection(self) -> bool:
        """创建全新的连接用于抓包（确保能捕获握手包）"""
        try:
            # 如果使用C语言执行器
            if self.use_c_executor and self._c_executor_service:
                logger.info("Creating fresh GaussDB connection using C executor")
                # 先关闭现有连接
                await self._c_executor_service.close_persistent_connection()
                # 等待一小段时间确保连接完全关闭
                await asyncio.sleep(0.5)
                # 创建新连接
                success = await self._c_executor_service.create_persistent_connection(
                    self.host, self.port, self.user, self.password, self.database
                )
                if success:
                    logger.info("Fresh GaussDB C executor connection created successfully")
                    return True
                else:
                    logger.error("Failed to create fresh GaussDB C executor connection")
                    return False

            # 使用Python执行器
            # 强制关闭现有连接
            if self._persistent_connection:
                try:
                    self._persistent_connection.close()
                except Exception as e:
                    logger.debug(f"Error closing existing GaussDB connection: {e}")
                self._persistent_connection = None

            # 等待一小段时间确保连接完全关闭
            await asyncio.sleep(0.5)

            logger.info("Creating fresh GaussDB connection")
            self._persistent_connection = await asyncio.get_event_loop().run_in_executor(
                None, self._create_connection
            )

            # 测试连接
            if GAUSSDB_DRIVER_AVAILABLE and gaussdb_module:
                with self._persistent_connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
            else:
                with self._persistent_connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()

            logger.info("Fresh GaussDB connection created successfully for packet capture")
            return True

        except Exception as e:
            logger.error(f"Failed to create fresh GaussDB connection: {str(e)}")
            if self._persistent_connection:
                try:
                    self._persistent_connection.close()
                except Exception as e:
                    logger.error(f"操作失败: {type(e).__name__}: {str(e)}")
                    logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                self._persistent_connection = None
            return False

    async def close_persistent_connection(self):
        """关闭持久连接"""
        try:
            # 如果使用C语言执行器
            if self.use_c_executor and self._c_executor_service:
                logger.info("Closing persistent GaussDB C executor connection")
                await self._c_executor_service.close_persistent_connection()
                logger.info("Persistent GaussDB C executor connection closed")
                return

            # 使用Python执行器
            if self._persistent_connection:
                logger.info("Closing persistent GaussDB connection")
                self._persistent_connection.close()
                self._persistent_connection = None
                logger.info("Persistent GaussDB connection closed")
        except Exception as e:
            logger.error(f"Error closing persistent GaussDB connection: {str(e)}")

    async def execute_sql_query_with_persistent_connection(self, sql_query: str) -> Dict[str, Any]:
        """使用持久连接执行GaussDB SQL查询（用于抓包场景）"""
        try:
            # 如果使用C语言执行器
            if self.use_c_executor and self._c_executor_service:
                logger.info(f"Executing GaussDB query with C executor persistent connection: {sql_query}")
                result = await self._c_executor_service.execute_sql_with_persistent_connection(sql_query)

                if result.get("success"):
                    # 转换C执行器结果格式为Python格式
                    return self._convert_c_result_to_python_format(result, sql_query)
                else:
                    raise Exception(f"C executor query failed: {result.get('error')}")

            # 使用Python执行器
            if not self._persistent_connection:
                raise Exception("No persistent connection available. Call create_persistent_connection first.")

            logger.info(f"Executing GaussDB query with persistent connection: {sql_query}")

            # 在线程池中执行查询
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._execute_query_sync_persistent, sql_query)

            logger.info(f"GaussDB query executed successfully with persistent connection")
            return result

        except Exception as e:
            logger.error(f"Failed to execute GaussDB query with persistent connection: {str(e)}")
            raise Exception(f"GaussDB query execution failed: {str(e)}")

    async def force_close_connection(self):
        """强制关闭连接（确保能捕获挥手包）"""
        try:
            # 如果使用C语言执行器
            if self.use_c_executor and self._c_executor_service:
                logger.info("Force closing GaussDB C executor connection for packet capture")
                await self._c_executor_service.close_persistent_connection()
                logger.info("GaussDB C executor connection force closed successfully")
                return

            # 使用Python执行器
            if self._persistent_connection:
                logger.info("Force closing GaussDB connection for packet capture")

                # 执行一个简单查询确保连接活跃
                try:
                    with self._persistent_connection.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                    logger.debug("GaussDB connection is active before closing")
                except Exception as e:
                    logger.debug(f"GaussDB connection test failed before closing: {e}")

                # 强制关闭连接
                self._persistent_connection.close()
                self._persistent_connection = None

                # 等待一小段时间确保挥手包被发送
                await asyncio.sleep(0.5)

                logger.info("GaussDB connection force closed successfully")
            else:
                logger.warning("No persistent GaussDB connection to close")

        except Exception as e:
            logger.error(f"Error during GaussDB force close: {str(e)}")
            self._persistent_connection = None

    async def execute_query_for_capture(self, sql_query: str) -> Dict[str, Any]:
        """专门用于抓包的SQL执行方法（包含完整的连接生命周期）"""
        try:
            logger.info("Starting GaussDB SQL execution for packet capture")

            # 1. 创建全新连接（确保捕获握手包）
            if not await self.create_fresh_connection():
                raise Exception("Failed to create fresh GaussDB connection for packet capture")

            # 2. 等待一小段时间确保连接建立完成
            await asyncio.sleep(0.5)

            # 3. 执行SQL查询
            logger.info(f"Executing GaussDB SQL for capture: {sql_query}")
            result = await self.execute_sql_query_with_persistent_connection(sql_query)

            # 4. 等待一小段时间确保查询完成
            await asyncio.sleep(0.5)

            logger.info("GaussDB SQL execution for packet capture completed")
            return result

        except Exception as e:
            logger.error(f"GaussDB SQL execution for capture failed: {str(e)}")
            raise

    def _execute_query_sync_persistent(self, sql_query: str) -> Dict[str, Any]:
        """使用持久连接同步执行SQL查询"""
        try:
            if not self._persistent_connection:
                raise Exception("No persistent connection available")

            # 设置查询超时（防止长时间运行的查询卡住）
            timeout_seconds = 300  # 默认5分钟

            # 对于大数据量插入，增加超时时间
            if 'INSERT' in sql_query.upper() and 'generate_series' in sql_query.lower():
                logger.info("检测到大数据量插入操作，设置更长的超时时间")
                timeout_seconds = 600  # 10分钟

            if GAUSSDB_DRIVER_AVAILABLE and gaussdb_module:
                # 使用GaussDB专用驱动
                with self._persistent_connection.cursor() as cursor:
                    # 设置语句超时
                    cursor.execute(f"SET statement_timeout = '{timeout_seconds}s'")
                    self._persistent_connection.commit()

                    query = sql_query.strip().rstrip(';')
                    cursor.execute(query)

                    query_upper = sql_query.upper().strip()
                    if query_upper.startswith('SELECT') or query_upper.startswith('WITH'):
                        rows = cursor.fetchall()
                        # 转换为字典列表
                        columns = [desc[0] for desc in cursor.description] if cursor.description else []
                        data = [dict(zip(columns, row)) for row in rows]

                        return {
                            'type': 'query',
                            'data': data,
                            'count': len(data),
                            'columns': columns
                        }
                    else:
                        self._persistent_connection.commit()
                        return {
                            'type': 'modification',
                            'message': 'Command executed successfully'
                        }
            else:
                # 使用PostgreSQL驱动
                import psycopg2.extras
                with self._persistent_connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    # 设置语句超时
                    cursor.execute(f"SET statement_timeout = '{timeout_seconds}s'")
                    self._persistent_connection.commit()

                    query = sql_query.strip().rstrip(';')
                    cursor.execute(query)

                    query_upper = query.upper().strip()
                    if query_upper.startswith('SELECT') or query_upper.startswith('WITH'):
                        rows = cursor.fetchall()
                        data = [dict(row) for row in rows]

                        return {
                            'type': 'query',
                            'data': data,
                            'count': len(data),
                            'columns': [desc[0] for desc in cursor.description] if cursor.description else []
                        }
                    else:
                        self._persistent_connection.commit()
                        affected_rows = cursor.rowcount
                        return {
                            'type': 'modification',
                            'affected_rows': affected_rows,
                            'message': f'Command executed successfully, {affected_rows} rows affected'
                        }

        except Exception as e:
            logger.error(f"GaussDB persistent query execution error: {str(e)}")
            raise

    def _convert_c_result_to_python_format(self, c_result: Dict[str, Any], sql_query: str) -> Dict[str, Any]:
        """将C执行器结果转换为Python格式"""
        try:
            data = c_result.get("data", {})

            # 检查是否是查询结果
            if "columns" in data and "rows" in data:
                # SELECT查询结果
                columns = data["columns"]
                rows = data["rows"]

                # 转换为字典列表
                result_data = []
                for row in rows:
                    row_dict = {}
                    for i, col_name in enumerate(columns):
                        row_dict[col_name] = row[i] if i < len(row) else None
                    result_data.append(row_dict)

                return {
                    'type': 'query',
                    'data': result_data,
                    'columns': columns,
                    'row_count': data.get("row_count", len(rows))
                }

            elif "affected_rows" in data:
                # INSERT/UPDATE/DELETE结果
                return {
                    'type': 'modification',
                    'affected_rows': data["affected_rows"],
                    'message': f'Command executed successfully, {data["affected_rows"]} rows affected'
                }

            else:
                # 其他命令结果
                return {
                    'type': 'command',
                    'message': 'Command executed successfully'
                }

        except Exception as e:
            logger.error(f"Failed to convert C result format: {e}")
            return {
                'type': 'error',
                'message': f'Result format conversion failed: {str(e)}'
            }

    async def close(self):
        """关闭服务"""
        await self.close_persistent_connection()
        self.close_pool()
        logger.info("GaussDB service V2 closed")

# 工厂函数
def get_gaussdb_service_v2(host: str, port: int, user: str, password: str, database: str,
                          use_c_executor: bool = False) -> GaussDBServiceV2:
    """获取GaussDB服务V2实例"""
    return GaussDBServiceV2(host, port, user, password, database, use_c_executor)
