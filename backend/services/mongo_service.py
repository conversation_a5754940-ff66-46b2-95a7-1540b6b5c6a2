import logging
import traceback
import async<PERSON>
from typing import Dict, Any, List, Optional
from datetime import timezone, timedelta
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure, ServerSelectionTimeoutError
import re
import json
from bson import ObjectId
from urllib.parse import quote_plus
from utils.config import Config
from utils.environment_detector import get_environment_detector
import subprocess
import tempfile
import os

logger = logging.getLogger(__name__)

class MongoService:
    """MongoDB服务类"""
    
    def __init__(self, host: str = None, port: int = None, user: str = None, 
                 password: str = None, database: str = None, auth_source: str = None):
        """初始化MongoDB服务"""
        self.host = host or Config.MONGO_HOST
        self.port = port or Config.MONGO_PORT
        self.user = user or Config.MONGO_USER
        self.password = password or Config.MONGO_PASSWORD
        self.database = database or Config.MONGO_DATABASE
        self.auth_source = auth_source or Config.MONGO_AUTH_SOURCE
        
        self.client = None
        self.db = None
        # 添加持久连接支持
        self._persistent_client = None
        self._persistent_db = None

        # 环境检测和C执行器配置
        self.env_detector = get_environment_detector()
        self.use_c_executor, self.c_executor_path = self.env_detector.should_use_c_executor('mongodb')

        logger.info(f"MongoDB Service initialized for {self.host}:{self.port}")
        logger.info(f"Environment: {self.env_detector.system} ({self.env_detector.machine})")
        logger.info(f"Docker: {self.env_detector.is_docker}")
        logger.info(f"C Executor: {'Enabled' if self.use_c_executor else 'Disabled'}")
        if self.c_executor_path:
            logger.info(f"C Executor Path: {self.c_executor_path}")
    
    async def initialize(self):
        """初始化MongoDB连接"""
        try:
            await self.connect()
            logger.info("MongoDB service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize MongoDB service: {str(e)}")
            raise
    
    async def connect(self) -> bool:
        """连接到MongoDB"""
        try:
            # 构建连接URI
            if self.user and self.password:
                # 对用户名和密码进行URL编码以处理特殊字符
                encoded_user = quote_plus(self.user)
                encoded_password = quote_plus(self.password)
                uri = f"mongodb://{encoded_user}:{encoded_password}@{self.host}:{self.port}/{self.database}?authSource={self.auth_source}&directConnection=true"
            else:
                uri = f"mongodb://{self.host}:{self.port}/{self.database}?directConnection=true"
            
            # 创建客户端连接，使用更温和的连接参数
            self.client = MongoClient(
                uri,
                serverSelectionTimeoutMS=5000,  # 5秒超时
                connectTimeoutMS=5000,
                socketTimeoutMS=5000,
                tz_aware=True,  # 启用时区感知
                tzinfo=timezone(timedelta(hours=8)),  # 设置为+8时区
                maxPoolSize=1,  # 限制连接池大小
                minPoolSize=0,  # 最小连接池大小
                maxIdleTimeMS=30000,  # 30秒空闲超时
                waitQueueTimeoutMS=5000,  # 等待队列超时
                heartbeatFrequencyMS=10000,  # 心跳频率
                retryWrites=False,  # 禁用重试写入
                retryReads=False   # 禁用重试读取
            )
            
            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self.client.admin.command, 'ping'
            )
            
            # 选择数据库
            self.db = self.client[self.database]
            
            logger.info(f"Connected to MongoDB: {self.host}:{self.port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {str(e)}")
            return False
    
    async def check_connection(self) -> bool:
        """检查MongoDB连接状态"""
        try:
            if not self.client:
                return await self.connect()
            
            # 执行ping命令测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self.client.admin.command, 'ping'
            )
            return True
            
        except Exception as e:
            logger.error(f"MongoDB connection check failed: {str(e)}")
            return False

    async def create_persistent_connection(self) -> bool:
        """创建持久连接用于抓包"""
        try:
            if self._persistent_client:
                # 检查现有连接是否有效
                try:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self._persistent_client.admin.command, 'ping'
                    )
                    logger.info("Existing MongoDB persistent connection is valid")
                    return True
                except:
                    # 现有连接无效，关闭并重新创建
                    try:
                        self._persistent_client.close()
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                    self._persistent_client = None
                    self._persistent_db = None

            # 构建连接URI
            if self.user and self.password:
                # 对用户名和密码进行URL编码以处理特殊字符
                encoded_user = quote_plus(self.user)
                encoded_password = quote_plus(self.password)
                uri = f"mongodb://{encoded_user}:{encoded_password}@{self.host}:{self.port}/{self.database}?authSource={self.auth_source}&directConnection=true"
            else:
                uri = f"mongodb://{self.host}:{self.port}/{self.database}?directConnection=true"

            # 创建新的持久连接，使用更温和的连接参数
            self._persistent_client = MongoClient(
                uri,
                serverSelectionTimeoutMS=5000,  # 5秒超时
                connectTimeoutMS=5000,
                socketTimeoutMS=5000,
                maxPoolSize=1,  # 限制连接池大小
                minPoolSize=0,  # 最小连接池大小
                maxIdleTimeMS=30000,  # 30秒空闲超时
                waitQueueTimeoutMS=5000,  # 等待队列超时
                heartbeatFrequencyMS=10000,  # 心跳频率
                retryWrites=False,  # 禁用重试写入
                retryReads=False   # 禁用重试读取
            )

            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_client.admin.command, 'ping'
            )

            # 选择数据库
            self._persistent_db = self._persistent_client[self.database]

            logger.info("Persistent MongoDB connection created successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to create persistent MongoDB connection: {str(e)}")
            self._persistent_client = None
            self._persistent_db = None
            return False

    async def create_fresh_connection(self) -> bool:
        """创建全新的连接用于抓包（确保能捕获握手包）"""
        try:
            # 强制关闭现有连接
            if self._persistent_client:
                try:
                    self._persistent_client.close()
                except Exception as e:
                    logger.debug(f"Error closing existing MongoDB connection: {e}")
                self._persistent_client = None
                self._persistent_db = None

            # 等待一小段时间确保连接完全关闭
            await asyncio.sleep(0.5)

            # 构建连接URI
            if self.user and self.password:
                # 对用户名和密码进行URL编码以处理特殊字符
                encoded_user = quote_plus(self.user)
                encoded_password = quote_plus(self.password)
                uri = f"mongodb://{encoded_user}:{encoded_password}@{self.host}:{self.port}/{self.database}?authSource={self.auth_source}&directConnection=true"
            else:
                uri = f"mongodb://{self.host}:{self.port}/{self.database}?directConnection=true"

            # 创建全新的连接，使用更温和的连接参数
            self._persistent_client = MongoClient(
                uri,
                serverSelectionTimeoutMS=5000,  # 5秒超时
                connectTimeoutMS=5000,
                socketTimeoutMS=5000,
                maxPoolSize=1,  # 限制连接池大小
                minPoolSize=0,  # 最小连接池大小
                maxIdleTimeMS=30000,  # 30秒空闲超时
                waitQueueTimeoutMS=5000,  # 等待队列超时
                heartbeatFrequencyMS=10000,  # 心跳频率
                retryWrites=False,  # 禁用重试写入
                retryReads=False   # 禁用重试读取
            )

            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_client.admin.command, 'ping'
            )

            # 选择数据库
            self._persistent_db = self._persistent_client[self.database]

            logger.info("Fresh MongoDB connection created successfully for packet capture")
            return True

        except Exception as e:
            logger.error(f"Failed to create fresh MongoDB connection: {str(e)}")
            self._persistent_client = None
            self._persistent_db = None
            return False

    async def close_persistent_connection(self):
        """关闭持久连接 - 使用多种策略避免RST包"""
        try:
            if self._persistent_client:
                # 策略1: 逐步减少连接活动
                try:
                    # 执行一个轻量级命令确保连接活跃
                    await asyncio.get_event_loop().run_in_executor(
                        None, self._persistent_client.admin.command, {'ping': 1}
                    )
                    logger.debug("MongoDB ping successful before closing")

                    # 等待命令完成
                    await asyncio.sleep(0.1)

                except Exception as e:
                    logger.debug(f"Ping failed before closing: {e}")

                # 策略2: 尝试优雅的会话结束
                try:
                    # 执行endSessions命令（如果支持）
                    await asyncio.get_event_loop().run_in_executor(
                        None, self._persistent_client.admin.command, {'endSessions': []}
                    )
                    logger.debug("MongoDB endSessions executed")
                except:
                    # 如果不支持endSessions，尝试logout
                    try:
                        await asyncio.get_event_loop().run_in_executor(
                            None, self._persistent_client.admin.command, {'logout': 1}
                        )
                        logger.debug("MongoDB logout executed")
                    except:
                        logger.debug("Neither endSessions nor logout supported")

                # 策略3: 让连接自然空闲一段时间
                logger.debug("Waiting for connection to idle...")
                await asyncio.sleep(0.5)

                # 策略4: 使用温和的关闭方式
                logger.debug("Closing MongoDB connection...")

                # 不直接调用close()，而是让连接自然超时
                # 将客户端引用设为None，让垃圾回收器处理
                old_client = self._persistent_client
                self._persistent_client = None
                self._persistent_db = None

                # 在后台线程中关闭连接，避免阻塞
                def close_in_background():
                    try:
                        old_client.close()
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                await asyncio.get_event_loop().run_in_executor(None, close_in_background)

                # 最后等待确保关闭完成
                await asyncio.sleep(0.5)

                logger.info("Persistent MongoDB connection closed with graceful strategy")
        except Exception as e:
            logger.error(f"Error closing persistent MongoDB connection: {str(e)}")
            self._persistent_client = None
            self._persistent_db = None

    async def force_close_connection(self):
        """强制关闭连接（确保能捕获挥手包）"""
        try:
            if self._persistent_client:
                logger.info("Force closing MongoDB connection for packet capture")

                # 执行一个简单查询确保连接活跃
                try:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self._persistent_client.admin.command, {'ping': 1}
                    )
                    logger.debug("MongoDB connection is active before closing")
                except Exception as e:
                    logger.debug(f"MongoDB connection test failed before closing: {e}")

                # 强制关闭连接
                self._persistent_client.close()
                self._persistent_client = None
                self._persistent_db = None

                # 等待一小段时间确保挥手包被发送
                await asyncio.sleep(0.5)

                logger.info("MongoDB connection force closed successfully")
            else:
                logger.warning("No persistent MongoDB connection to close")

        except Exception as e:
            logger.error(f"Error during MongoDB force close: {str(e)}")
            self._persistent_client = None
            self._persistent_db = None

    async def execute_mongo_query(self, mongo_query: str) -> Dict[str, Any]:
        """执行MongoDB查询"""
        try:
            if not await self.check_connection():
                raise Exception("MongoDB connection failed")

            logger.info(f"Executing MongoDB query: {mongo_query}")

            # 解析MongoDB查询
            result = await self._parse_and_execute_query(mongo_query)

            logger.info("MongoDB query executed successfully")
            return result

        except Exception as e:
            logger.error(f"Failed to execute MongoDB query: {str(e)}")
            raise Exception(f"MongoDB query execution failed: {str(e)}")

    async def execute_query(self, mongo_query: str) -> Dict[str, Any]:
        """执行查询的通用方法（兼容性别名）"""
        return await self.execute_mongo_query(mongo_query)

    async def execute_mongo_query_with_persistent_connection(self, mongo_query: str) -> Dict[str, Any]:
        """使用持久连接执行MongoDB查询（用于抓包场景）"""
        try:
            if self._persistent_client is None or self._persistent_db is None:
                raise Exception("No persistent connection available. Call create_persistent_connection first.")

            logger.info(f"Executing MongoDB query with persistent connection: {mongo_query}")

            # 解析MongoDB查询
            result = await self._parse_and_execute_query_persistent(mongo_query)

            logger.info("MongoDB query executed successfully with persistent connection")
            return result

        except Exception as e:
            logger.error(f"Failed to execute MongoDB query with persistent connection: {str(e)}")
            raise Exception(f"MongoDB persistent query execution failed: {str(e)}")

    async def execute_query_for_capture(self, mongo_query: str) -> Dict[str, Any]:
        """专门用于抓包的MongoDB查询执行方法（包含完整的连接生命周期）"""
        try:
            logger.info("Starting MongoDB query execution for packet capture")

            # 1. 创建全新连接（确保捕获握手包）
            if not await self.create_fresh_connection():
                raise Exception("Failed to create fresh MongoDB connection for packet capture")

            # 2. 等待一小段时间确保连接建立完成
            await asyncio.sleep(0.5)

            # 3. 执行MongoDB查询
            logger.info(f"Executing MongoDB query for capture: {mongo_query}")
            result = await self.execute_mongo_query_with_persistent_connection(mongo_query)

            # 4. 等待一小段时间确保查询完成
            await asyncio.sleep(0.5)

            logger.info("MongoDB query execution for packet capture completed")
            return result

        except Exception as e:
            logger.error(f"MongoDB query execution for capture failed: {str(e)}")
            raise

    async def _parse_and_execute_query_persistent(self, mongo_query: str) -> Dict[str, Any]:
        """使用持久连接解析并执行MongoDB查询"""
        try:
            # 移除可能的分号和空白字符
            query = mongo_query.strip().rstrip(';')

            # 解析不同类型的MongoDB操作
            if query.startswith('db.'):
                # 处理db.adminCommand({listDatabases: 1})这样的特殊命令
                if 'adminCommand' in query and 'listDatabases' in query:
                    return await self._execute_show_command_persistent('show databases')
                return await self._execute_db_operation_persistent(query)
            elif query.startswith('use '):
                return await self._execute_use_command_persistent(query)
            elif query.startswith('show '):
                return await self._execute_show_command_persistent(query)
            else:
                # 尝试处理一些常见的自然语言转换结果
                if 'find' in query.lower() or 'select' in query.lower():
                    # 尝试转换为find操作
                    return await self._execute_db_operation_persistent(f"db.collection.find()")
                else:
                    raise Exception(f"Unsupported MongoDB query format: {query}")

        except Exception as e:
            logger.error(f"Failed to parse and execute persistent MongoDB query: {str(e)}")
            raise

    async def _parse_and_execute_query(self, mongo_query: str) -> Dict[str, Any]:
        """解析并执行MongoDB查询"""
        try:
            # 移除可能的分号和空白字符
            query = mongo_query.strip().rstrip(';')

            # 检查是否包含JavaScript for循环
            if 'for' in query and '{' in query and '}' in query and 'db.' in query:
                logger.info("Detected JavaScript for loop, executing iteratively")
                results = await self._execute_javascript_for_loop(query)
                return {
                    'type': 'batch',
                    'operation': 'for_loop',
                    'results': results,
                    'count': len(results)
                }

            # 检查是否包含多个MongoDB命令（用分号分隔）
            if ';' in query and query.count('db.') > 1:
                return await self._execute_multiple_commands(query)

            # 解析不同类型的MongoDB操作
            if query.startswith('db.'):
                # 处理db.adminCommand({listDatabases: 1})这样的特殊命令
                if 'adminCommand' in query and 'listDatabases' in query:
                    return await self._execute_show_command('show databases')
                return await self._execute_db_operation(query)
            elif query.startswith('sh.'):
                # 处理分片操作
                return await self._execute_shard_operation(query)
            elif query.startswith('use '):
                return await self._execute_use_command(query)
            elif query.startswith('show '):
                return await self._execute_show_command(query)
            else:
                # 尝试处理一些常见的自然语言转换结果
                if 'listDatabases' in query or 'list databases' in query:
                    return await self._execute_show_command('show databases')
                elif 'listCollections' in query or 'list collections' in query:
                    return await self._execute_show_command('show collections')
                else:
                    raise Exception(f"Unsupported MongoDB operation: {query}")

        except Exception as e:
            logger.error(f"Failed to parse MongoDB query: {str(e)}")
            raise

    async def _execute_chained_operation(self, query: str) -> Dict[str, Any]:
        """执行链式操作，如 db.test.find().limit(5) 或 db.test.find().explain()"""
        try:
            # 解析基础操作和链式方法
            # 例如: db.test.find().limit(5) -> base: db.test.find(), chains: [limit(5)]

            # 首先找到基础操作，使用更精确的解析
            base_operation, remaining_query = self._parse_base_operation_with_chains(query)

            # 提取集合名和基础操作
            pattern = r'db\.(\w+)\.(\w+)\((.*)\)'
            match = re.match(pattern, base_operation)

            if not match:
                raise Exception(f"Invalid base operation format: {base_operation}")

            collection_name = match.group(1)
            operation = match.group(2)
            params = match.group(3)

            collection = self.db[collection_name] if self.db else None

            # 解析链式方法
            chain_methods = self._parse_chain_methods(remaining_query)

            # 执行基础操作
            if operation == 'find':
                result = await self._execute_find_with_chains(collection, params, chain_methods)
            elif operation == 'aggregate':
                result = await self._execute_aggregate_with_chains(collection, params, chain_methods)
            else:
                raise Exception(f"Chained operations not supported for: {operation}")

            return result

        except Exception as e:
            logger.error(f"Failed to execute chained operation: {str(e)}")
            raise
    
    async def _execute_db_operation(self, query: str) -> Dict[str, Any]:
        """执行db.collection操作或db级别操作"""
        try:
            # 处理链式调用，如 db.test.find().limit(5) 或 db.test.find().explain()
            if ('.limit(' in query or '.skip(' in query or '.sort(' in query or
                '.explain(' in query):
                return await self._execute_chained_operation(query)

            # 首先检查是否是数据库级别的操作（如 db.createCollection）
            db_level_pattern = r'db\.(\w+)\((.*)\)'
            db_match = re.match(db_level_pattern, query)

            if db_match:
                operation = db_match.group(1)
                params = db_match.group(2)

                # 处理数据库级别的操作
                if operation == 'createCollection':
                    return await self._execute_create_collection(params)
                elif operation == 'createView':
                    return await self._execute_create_view(params)
                elif operation == 'dropDatabase':
                    return await self._execute_drop_database(params)
                elif operation == 'runCommand':
                    return await self._execute_run_command(params)
                else:
                    # 如果不是已知的数据库级别操作，继续尝试集合级别操作
                    pass

            # 提取集合名和操作
            pattern = r'db\.(\w+)\.(\w+)\((.*)\)'
            match = re.match(pattern, query)

            if not match:
                raise Exception(f"Invalid MongoDB query format: {query}")

            collection_name = match.group(1)
            operation = match.group(2)
            params = match.group(3)

            # 如果没有数据库连接，传递None给操作方法
            collection = self.db[collection_name] if self.db else None
            
            # 根据操作类型执行相应的方法
            if operation == 'find':
                return await self._execute_find(collection, params)
            elif operation == 'findOne':
                return await self._execute_find_one(collection, params)
            elif operation == 'insertOne':
                return await self._execute_insert_one(collection, params)
            elif operation == 'insertMany':
                return await self._execute_insert_many(collection, params)
            elif operation == 'updateOne':
                return await self._execute_update_one(collection, params)
            elif operation == 'updateMany':
                return await self._execute_update_many(collection, params)
            elif operation == 'deleteOne':
                return await self._execute_delete_one(collection, params)
            elif operation == 'deleteMany':
                return await self._execute_delete_many(collection, params)
            elif operation == 'aggregate':
                return await self._execute_aggregate(collection, params)
            elif operation == 'countDocuments':
                return await self._execute_count(collection, params)
            elif operation == 'createIndex':
                return await self._execute_create_index(collection, params)
            elif operation == 'drop':
                return await self._execute_drop(collection, params)
            elif operation == 'getShardDistribution':
                return await self._execute_shard_distribution(collection, params)
            elif operation == 'stats':
                return await self._execute_collection_stats(collection, params)
            elif operation == 'explain':
                return await self._execute_explain(collection, params)
            else:
                raise Exception(f"Unsupported operation: {operation}")
                
        except Exception as e:
            logger.error(f"Failed to execute db operation: {str(e)}")
            raise
    
    async def _execute_find(self, collection, params: str) -> Dict[str, Any]:
        """执行find操作"""
        try:
            # 解析参数
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}
            
            # 执行查询
            cursor = await asyncio.get_event_loop().run_in_executor(
                None, collection.find, filter_doc
            )
            
            # 转换结果
            documents = []
            for doc in cursor:
                # 转换ObjectId为字符串
                doc = self._convert_objectid_to_str(doc)
                documents.append(doc)
            
            return {
                'type': 'query',
                'operation': 'find',
                'collection': collection.name,
                'data': documents,
                'count': len(documents)
            }
            
        except Exception as e:
            logger.error(f"Find operation failed: {str(e)}")
            raise

    async def _execute_find_with_chains(self, collection, params: str, chain_methods: list) -> Dict[str, Any]:
        """执行带链式方法的find操作"""
        try:
            # 解析基础查询参数
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}

            # 如果没有集合连接，返回模拟结果
            if not collection:
                return {
                    'type': 'query',
                    'operation': 'find_with_chains',
                    'filter': filter_doc,
                    'chain_methods': chain_methods,
                    'results': [],
                    'simulated': True
                }

            # 创建游标
            cursor = await asyncio.get_event_loop().run_in_executor(
                None, collection.find, filter_doc
            )

            # 应用链式方法
            limit_value = None
            skip_value = None
            sort_spec = None
            explain_mode = None

            for method_info in chain_methods:
                if isinstance(method_info, dict):
                    method_name = method_info['method']
                    method_params = method_info['params']
                else:
                    # 兼容旧格式
                    method_name, method_params = method_info

                if method_name == 'limit':
                    try:
                        limit_value = int(method_params) if method_params else 0
                    except ValueError:
                        raise Exception(f"Invalid limit value: {method_params}")
                elif method_name == 'skip':
                    try:
                        skip_value = int(method_params) if method_params else 0
                    except ValueError:
                        raise Exception(f"Invalid skip value: {method_params}")
                elif method_name == 'sort':
                    if method_params.strip():
                        sort_spec = self._parse_json_params(method_params)
                elif method_name == 'explain':
                    # 解析explain参数，如'executionStats'
                    explain_mode = method_params.strip().strip("'\"") if method_params.strip() else 'queryPlanner'
                else:
                    logger.warning(f"Unsupported chain method: {method_name}")

            # 如果有explain调用，返回执行计划而不是实际数据
            if explain_mode:
                # 应用其他链式操作到游标
                if sort_spec:
                    cursor = cursor.sort(list(sort_spec.items()))
                if skip_value:
                    cursor = cursor.skip(skip_value)
                if limit_value:
                    cursor = cursor.limit(limit_value)

                # 获取执行计划
                explain_result = await asyncio.get_event_loop().run_in_executor(
                    None, cursor.explain, explain_mode
                )

                return {
                    'type': 'explain',
                    'operation': 'find',
                    'collection': collection.name,
                    'explain_mode': explain_mode,
                    'execution_plan': explain_result,
                    'acknowledged': True
                }

            # 应用链式操作到游标
            if sort_spec:
                cursor = cursor.sort(list(sort_spec.items()))
            if skip_value:
                cursor = cursor.skip(skip_value)
            if limit_value:
                cursor = cursor.limit(limit_value)

            # 转换结果
            documents = []
            for doc in cursor:
                # 转换ObjectId为字符串
                doc = self._convert_objectid_to_str(doc)
                documents.append(doc)

            return {
                'type': 'query',
                'operation': 'find',
                'collection': collection.name,
                'data': documents,
                'count': len(documents),
                'applied_chains': [f"{name}({params})" for name, params in chain_methods]
            }

        except Exception as e:
            logger.error(f"Find with chains operation failed: {str(e)}")
            raise

    async def _execute_aggregate_with_chains(self, collection, params: str, chain_methods: list) -> Dict[str, Any]:
        """执行带链式方法的aggregate操作（暂时不支持）"""
        raise Exception("Chained operations for aggregate are not yet supported")

    async def _execute_aggregate(self, collection, params: str) -> Dict[str, Any]:
        """执行aggregate操作"""
        try:
            # 解析聚合管道
            if params.strip():
                pipeline = self._parse_json_params(params)
            else:
                pipeline = []

            # 如果没有集合连接，返回模拟结果
            if not collection:
                return {
                    'type': 'aggregate',
                    'operation': 'aggregate',
                    'pipeline': pipeline,
                    'results': [],
                    'simulated': True
                }

            # 执行聚合查询
            cursor = await asyncio.get_event_loop().run_in_executor(
                None, collection.aggregate, pipeline
            )

            # 转换结果
            documents = []
            for doc in cursor:
                # 转换ObjectId为字符串
                doc = self._convert_objectid_to_str(doc)
                documents.append(doc)

            return {
                'type': 'aggregate',
                'operation': 'aggregate',
                'pipeline': pipeline,
                'results': documents,
                'count': len(documents)
            }

        except Exception as e:
            logger.error(f"Aggregate operation failed: {str(e)}")
            raise
    
    async def _execute_insert_one(self, collection, params: str) -> Dict[str, Any]:
        """执行insertOne操作"""
        try:
            document = self._parse_json_params(params)

            result = await asyncio.get_event_loop().run_in_executor(
                None, collection.insert_one, document
            )

            return {
                'type': 'modification',
                'operation': 'insertOne',
                'collection': collection.name,
                'inserted_id': str(result.inserted_id),
                'acknowledged': result.acknowledged,
                'message': f"Successfully inserted 1 document"
            }

        except Exception as e:
            logger.error(f"Insert operation failed: {str(e)}")
            raise

    async def _execute_insert_many(self, collection, params: str) -> Dict[str, Any]:
        """执行insertMany操作"""
        try:
            documents = self._parse_json_params(params)

            # 确保documents是一个列表
            if not isinstance(documents, list):
                raise ValueError("insertMany requires an array of documents")

            result = await asyncio.get_event_loop().run_in_executor(
                None, collection.insert_many, documents
            )

            return {
                'type': 'modification',
                'operation': 'insertMany',
                'collection': collection.name,
                'inserted_ids': [str(id) for id in result.inserted_ids],
                'acknowledged': result.acknowledged,
                'message': f"Successfully inserted {len(result.inserted_ids)} documents"
            }

        except Exception as e:
            logger.error(f"Insert many operation failed: {str(e)}")
            raise
    
    def _parse_json_params(self, params: str) -> Dict[str, Any]:
        """解析JSON参数"""
        try:
            # 移除外层括号和空白字符
            params = params.strip()
            if params.startswith('(') and params.endswith(')'):
                params = params[1:-1]

            # 如果参数为空，返回空字典
            if not params.strip():
                return {}

            # 检查是否包含变量（如 {userId: i, data: 'Sample data ' + i}）
            if self._contains_variables(params):
                logger.warning(f"Parameters contain variables that need evaluation: {params}")
                # 尝试修复常见的变量模式
                params = self._fix_variable_params(params)

            # 首先尝试直接解析JSON
            try:
                return json.loads(params)
            except json.JSONDecodeError:
                # 如果失败，尝试修复JavaScript对象格式
                import re

                # 先修复单引号为双引号
                fixed_params = re.sub(r"'([^']*)'", r'"\1"', params)

                # 匹配未加引号的属性名（包括MongoDB操作符如$gt, $lt等）
                fixed_params = re.sub(r'(\$?\w+):', r'"\1":', fixed_params)

                # 修复MongoDB操作符（如$gt, $lt等）
                fixed_params = re.sub(r'"\$(\w+)":', r'"$\1":', fixed_params)

                # 再次尝试解析
                return json.loads(fixed_params)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON params: {params}, error: {str(e)}")
            raise Exception(f"Invalid JSON format in parameters: {params}")

    def _contains_variables(self, params: str) -> bool:
        """检查参数是否包含变量"""
        import re
        # 检查是否包含未引号的变量名（如 userId: i）
        variable_patterns = [
            r'\w+:\s*[a-zA-Z_]\w*(?!\s*["\'])',  # 变量赋值模式 (key: variable)
            r'\+\s*[a-zA-Z_]\w*',  # 字符串拼接模式 (+ variable)
            r'[a-zA-Z_]\w*\s*\+',  # 变量加法模式 (variable +)
            r':\s*[a-zA-Z_]\w*(?=\s*[,}])',  # 单独的变量值 (: variable,)
        ]

        for pattern in variable_patterns:
            if re.search(pattern, params):
                return True
        return False

    def _fix_variable_params(self, params: str) -> str:
        """修复包含变量的参数"""
        import re

        # 先修复单引号为双引号
        params = re.sub(r"'([^']*)'", r'"\1"', params)

        # 替换变量 i 为 1
        params = re.sub(r'\b(i|index|idx)\b(?!\s*["\'])', '1', params)

        # 处理字符串拼接表达式
        # 匹配模式: "text" + variable 或 variable + "text"
        # 例如: "user" + i -> "user1", i + "@example.com" -> "<EMAIL>"

        # 处理 "text" + variable 模式
        params = re.sub(r'"([^"]*?)"\s*\+\s*[a-zA-Z_]\w*', r'"\g<1>1"', params)

        # 处理 variable + "text" 模式
        params = re.sub(r'[a-zA-Z_]\w*\s*\+\s*"([^"]*?)"', r'"1\g<1>"', params)

        # 处理 Math.floor(Math.random() * 50) + 18 这样的表达式
        # 替换为固定值 35 (18 + 17)
        params = re.sub(r'Math\.floor\(Math\.random\(\)\s*\*\s*\d+\)\s*\+\s*\d+', '35', params)

        # 处理 Math.random() * 50 这样的表达式
        params = re.sub(r'Math\.random\(\)\s*\*\s*\d+', '25', params)

        # 处理 Math.random() 这样的表达式
        params = re.sub(r'Math\.random\(\)', '0.5', params)

        # 处理剩余的变量引用，替换为合理的默认值
        params = re.sub(r'\b[a-zA-Z_]\w*(?!\s*["\'])(?=\s*[,}])', '1', params)

        # 清理可能的语法错误
        # 移除多余的 + 号
        params = re.sub(r'\+\s*\+', '+', params)

        # 修复可能的引号不匹配问题
        # 如果字符串以引号开始但没有结束引号，添加结束引号
        params = re.sub(r'"([^"]*?)(?=\s*[,}])', r'"\1"', params)

        return params

    async def _execute_javascript_for_loop(self, query: str) -> List[Dict[str, Any]]:
        """执行JavaScript for循环，生成多个MongoDB操作"""
        import re

        results = []

        # 解析for循环参数
        for_pattern = r'for\s*\(\s*let\s+(\w+)\s*=\s*(\d+);\s*\1\s*<\s*(\d+);\s*\1\+\+\s*\)'
        for_match = re.search(for_pattern, query)

        if not for_match:
            logger.warning(f"Cannot parse for loop pattern: {query}")
            return results

        var_name = for_match.group(1)  # 通常是 'i'
        start_val = int(for_match.group(2))  # 开始值
        end_val = int(for_match.group(3))    # 结束值

        # 提取for循环体中的MongoDB命令
        loop_body_pattern = r'for\s*\([^)]*\)\s*\{\s*([^}]*)\s*\}'
        body_match = re.search(loop_body_pattern, query, re.DOTALL)

        if not body_match:
            logger.warning(f"Cannot extract for loop body: {query}")
            return results

        loop_body = body_match.group(1).strip()

        # 提取MongoDB命令模式
        mongo_cmd_pattern = r'db\.(\w+)\.(\w+)\(([^)]*)\)'
        mongo_match = re.search(mongo_cmd_pattern, loop_body)

        if not mongo_match:
            logger.warning(f"Cannot find MongoDB command in loop body: {loop_body}")
            return results

        collection_name = mongo_match.group(1)
        operation = mongo_match.group(2)
        params_template = mongo_match.group(3)

        # 执行循环，但限制最大执行次数以避免性能问题
        max_iterations = min(end_val - start_val, 100)  # 最多执行100次

        for i in range(start_val, start_val + max_iterations):
            try:
                # 替换参数中的变量
                params_str = params_template.replace(var_name, str(i))

                # 处理字符串拼接
                params_str = re.sub(r"'([^']*?)'\s*\+\s*" + str(i), f"'{r'\1'}{i}'", params_str)
                params_str = re.sub(str(i) + r"\s*\+\s*'([^']*?)'", f"'{i}{r'\1'}'", params_str)

                # 处理Math.random()等表达式
                params_str = re.sub(r'Math\.floor\(Math\.random\(\)\s*\*\s*\d+\)\s*\+\s*\d+', str(18 + i % 50), params_str)

                # 构造完整的MongoDB命令
                mongo_cmd = f"db.{collection_name}.{operation}({params_str})"

                # 执行单个MongoDB命令
                result = await self._execute_single_mongodb_command(mongo_cmd)
                results.append(result)

            except Exception as e:
                logger.error(f"Error executing iteration {i}: {str(e)}")
                continue

        return results

    async def _execute_multiple_commands(self, query: str) -> Dict[str, Any]:
        """执行多个MongoDB命令（用分号分隔）"""
        try:
            # 按分号分割命令
            commands = [cmd.strip() for cmd in query.split(';') if cmd.strip()]
            results = []

            for cmd in commands:
                if cmd.startswith('db.'):
                    result = await self._execute_db_operation(cmd)
                    results.append(result)
                elif cmd.startswith('use '):
                    result = await self._execute_use_command(cmd)
                    results.append(result)
                elif cmd.startswith('show '):
                    result = await self._execute_show_command(cmd)
                    results.append(result)
                else:
                    logger.warning(f"Skipping unsupported command: {cmd}")

            return {
                'type': 'batch',
                'operation': 'multiple_commands',
                'results': results,
                'count': len(results)
            }

        except Exception as e:
            logger.error(f"Error executing multiple commands: {str(e)}")
            raise

    async def _execute_single_mongodb_command(self, mongo_cmd: str) -> Dict[str, Any]:
        """执行单个MongoDB命令"""
        try:
            return await self._execute_db_operation(mongo_cmd)
        except Exception as e:
            logger.error(f"Error executing single MongoDB command: {str(e)}")
            raise

    def _convert_objectid_to_str(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """将ObjectId转换为字符串"""
        if isinstance(doc, dict):
            for key, value in doc.items():
                if isinstance(value, ObjectId):
                    doc[key] = str(value)
                elif isinstance(value, dict):
                    doc[key] = self._convert_objectid_to_str(value)
                elif isinstance(value, list):
                    doc[key] = [self._convert_objectid_to_str(item) if isinstance(item, dict) else item for item in value]
        return doc

    async def _execute_shard_distribution(self, collection, params: str) -> Dict[str, Any]:
        """执行getShardDistribution操作"""
        try:
            # getShardDistribution是一个集合级别的方法，返回分片分布信息
            # 对于非分片集合，返回基本统计信息
            stats = await asyncio.get_event_loop().run_in_executor(
                None, collection.estimated_document_count
            )

            return {
                'type': 'admin',
                'operation': 'getShardDistribution',
                'collection': collection.name,
                'data': {
                    'sharded': False,
                    'estimated_count': stats,
                    'message': 'Collection is not sharded or sharding info not available'
                },
                'count': 1
            }

        except Exception as e:
            logger.error(f"Shard distribution operation failed: {str(e)}")
            # 返回默认信息而不是抛出异常
            return {
                'type': 'admin',
                'operation': 'getShardDistribution',
                'collection': collection.name,
                'data': {
                    'sharded': False,
                    'message': f'Unable to get shard distribution: {str(e)}'
                },
                'count': 0
            }

    async def _execute_collection_stats(self, collection, params: str) -> Dict[str, Any]:
        """执行stats操作"""
        try:
            # 获取集合统计信息
            db = collection.database
            stats_result = await asyncio.get_event_loop().run_in_executor(
                None, db.command, "collStats", collection.name
            )

            return {
                'type': 'admin',
                'operation': 'stats',
                'collection': collection.name,
                'data': stats_result,
                'count': 1
            }

        except Exception as e:
            logger.error(f"Collection stats operation failed: {str(e)}")
            raise

    async def _execute_explain(self, collection, params: str) -> Dict[str, Any]:
        """执行explain操作"""
        try:
            # 解析查询参数
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}

            # 如果没有集合连接，返回模拟结果
            if not collection:
                return {
                    'type': 'explain',
                    'operation': 'explain',
                    'filter': filter_doc,
                    'data': {'simulated': True, 'executionStats': {'totalDocsExamined': 0}},
                    'simulated': True
                }

            # 执行explain
            cursor = collection.find(filter_doc)
            explain_result = await asyncio.get_event_loop().run_in_executor(
                None, cursor.explain
            )

            return {
                'type': 'admin',
                'operation': 'explain',
                'collection': collection.name,
                'data': explain_result,
                'count': 1
            }

        except Exception as e:
            logger.error(f"Explain operation failed: {str(e)}")
            raise

    async def get_databases(self) -> List[str]:
        """获取数据库列表"""
        try:
            if not await self.check_connection():
                raise Exception("MongoDB connection failed")
            
            db_list = await asyncio.get_event_loop().run_in_executor(
                None, self.client.list_database_names
            )
            
            return db_list
            
        except Exception as e:
            logger.error(f"Failed to get databases: {str(e)}")
            raise
    
    async def get_collections(self, database: str = None) -> List[str]:
        """获取集合列表"""
        try:
            if not await self.check_connection():
                raise Exception("MongoDB connection failed")
            
            db = self.client[database] if database else self.db
            
            collection_list = await asyncio.get_event_loop().run_in_executor(
                None, db.list_collection_names
            )
            
            return collection_list
            
        except Exception as e:
            logger.error(f"Failed to get collections: {str(e)}")
            raise
    
    async def _execute_find_one(self, collection, params: str) -> Dict[str, Any]:
        """执行findOne操作"""
        try:
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}

            document = await asyncio.get_event_loop().run_in_executor(
                None, collection.find_one, filter_doc
            )

            if document:
                document = self._convert_objectid_to_str(document)

            return {
                'type': 'query',
                'operation': 'findOne',
                'collection': collection.name,
                'data': [document] if document else [],
                'count': 1 if document else 0
            }

        except Exception as e:
            logger.error(f"FindOne operation failed: {str(e)}")
            raise

    async def _execute_update_one(self, collection, params: str) -> Dict[str, Any]:
        """执行updateOne操作"""
        try:
            # 解析参数 - updateOne需要filter和update两个参数
            params_list = self._parse_multiple_params(params)
            if len(params_list) < 2:
                raise Exception("updateOne requires filter and update parameters")

            filter_doc = params_list[0]
            update_doc = params_list[1]

            result = await asyncio.get_event_loop().run_in_executor(
                None, collection.update_one, filter_doc, update_doc
            )

            return {
                'type': 'modification',
                'operation': 'updateOne',
                'collection': collection.name,
                'matched_count': result.matched_count,
                'modified_count': result.modified_count,
                'acknowledged': result.acknowledged,
                'message': f"Matched {result.matched_count}, modified {result.modified_count} document(s)"
            }

        except Exception as e:
            logger.error(f"UpdateOne operation failed: {str(e)}")
            raise

    async def _execute_delete_one(self, collection, params: str) -> Dict[str, Any]:
        """执行deleteOne操作"""
        try:
            filter_doc = self._parse_json_params(params)

            result = await asyncio.get_event_loop().run_in_executor(
                None, collection.delete_one, filter_doc
            )

            return {
                'type': 'modification',
                'operation': 'deleteOne',
                'collection': collection.name,
                'deleted_count': result.deleted_count,
                'acknowledged': result.acknowledged,
                'message': f"Successfully deleted {result.deleted_count} document(s)"
            }

        except Exception as e:
            logger.error(f"DeleteOne operation failed: {str(e)}")
            raise

    async def _execute_count(self, collection, params: str) -> Dict[str, Any]:
        """执行countDocuments操作"""
        try:
            if params.strip():
                filter_doc = self._parse_json_params(params)
            else:
                filter_doc = {}

            # 如果没有集合连接，返回模拟结果
            if not collection:
                return {
                    'type': 'count',
                    'operation': 'countDocuments',
                    'filter': filter_doc,
                    'count': 0,
                    'simulated': True
                }

            count = await asyncio.get_event_loop().run_in_executor(
                None, collection.count_documents, filter_doc
            )

            return {
                'type': 'query',
                'operation': 'countDocuments',
                'collection': collection.name,
                'count': count,
                'message': f"Collection has {count} document(s)"
            }

        except Exception as e:
            logger.error(f"Count operation failed: {str(e)}")
            raise

    async def _execute_drop(self, collection, params: str) -> Dict[str, Any]:
        """执行drop操作 - 删除整个集合"""
        try:
            collection_name = collection.name

            # drop操作通常不需要参数，但为了安全起见，我们检查一下
            if params.strip():
                logger.warning(f"Drop operation received unexpected parameters: {params}")

            # 执行drop操作
            result = await asyncio.get_event_loop().run_in_executor(
                None, collection.drop
            )

            return {
                'type': 'modification',
                'operation': 'drop',
                'collection': collection_name,
                'message': f"Successfully dropped collection '{collection_name}'",
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Drop operation failed: {str(e)}")
            raise

    def _parse_multiple_params(self, params: str) -> List[Dict[str, Any]]:
        """解析多个JSON参数"""
        try:
            params = params.strip()
            if params.startswith('(') and params.endswith(')'):
                params = params[1:-1]

            # 简单的参数分割 - 寻找顶级逗号
            result = []
            bracket_count = 0
            current_param = ""

            for char in params:
                if char == '{':
                    bracket_count += 1
                elif char == '}':
                    bracket_count -= 1
                elif char == ',' and bracket_count == 0:
                    if current_param.strip():
                        # 使用_parse_json_params来处理单个参数，包含格式修复
                        param_dict = self._parse_json_params(current_param.strip())
                        result.append(param_dict)
                    current_param = ""
                    continue

                current_param += char

            # 添加最后一个参数
            if current_param.strip():
                # 使用_parse_json_params来处理单个参数，包含格式修复
                param_dict = self._parse_json_params(current_param.strip())
                result.append(param_dict)

            return result

        except Exception as e:
            logger.error(f"Failed to parse multiple params: {params}, error: {str(e)}")
            raise Exception(f"Invalid parameter format: {params}")

    async def _execute_use_command(self, query: str) -> Dict[str, Any]:
        """执行use命令"""
        try:
            # 提取数据库名
            db_name = query.replace('use ', '').strip()
            self.db = self.client[db_name]

            return {
                'type': 'command',
                'operation': 'use',
                'message': f"Switched to database: {db_name}"
            }

        except Exception as e:
            logger.error(f"Use command failed: {str(e)}")
            raise

    async def _execute_show_command(self, query: str) -> Dict[str, Any]:
        """执行show命令"""
        try:
            if 'databases' in query or 'dbs' in query:
                databases = await self.get_databases()
                return {
                    'type': 'query',
                    'operation': 'show databases',
                    'data': [{'name': db} for db in databases],
                    'count': len(databases)
                }
            elif 'collections' in query:
                collections = await self.get_collections()
                return {
                    'type': 'query',
                    'operation': 'show collections',
                    'data': [{'name': coll} for coll in collections],
                    'count': len(collections)
                }
            else:
                raise Exception(f"Unsupported show command: {query}")

        except Exception as e:
            logger.error(f"Show command failed: {str(e)}")
            raise

    async def close(self):
        """关闭MongoDB连接 - 使用多种策略避免RST包"""
        try:
            if self.client:
                # 策略1: 逐步减少连接活动
                try:
                    # 执行一个轻量级命令确保连接活跃
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.client.admin.command, {'ping': 1}
                    )
                    logger.debug("MongoDB ping successful before closing")

                    # 等待命令完成
                    await asyncio.sleep(0.1)

                except Exception as e:
                    logger.debug(f"Ping failed before closing: {e}")

                # 策略2: 尝试优雅的会话结束
                try:
                    # 执行endSessions命令（如果支持）
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.client.admin.command, {'endSessions': []}
                    )
                    logger.debug("MongoDB endSessions executed")
                except:
                    # 如果不支持endSessions，尝试logout
                    try:
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.client.admin.command, {'logout': 1}
                        )
                        logger.debug("MongoDB logout executed")
                    except:
                        logger.debug("Neither endSessions nor logout supported")

                # 策略3: 让连接自然空闲一段时间
                logger.debug("Waiting for connection to idle...")
                await asyncio.sleep(0.5)

                # 策略4: 使用温和的关闭方式
                logger.debug("Closing MongoDB connection...")

                # 不直接调用close()，而是让连接自然超时
                # 将客户端引用设为None，让垃圾回收器处理
                old_client = self.client
                self.client = None
                self.db = None

                # 在后台线程中关闭连接，避免阻塞
                def close_in_background():
                    try:
                        old_client.close()
                    except Exception as e:

                        logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

                        logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
                await asyncio.get_event_loop().run_in_executor(None, close_in_background)

                # 最后等待确保关闭完成
                await asyncio.sleep(0.5)

                logger.info("MongoDB connection closed with graceful strategy")
        except Exception as e:
            logger.error(f"Error closing MongoDB connection: {str(e)}")

    # 持久连接的执行方法
    async def _execute_db_operation_persistent(self, query: str) -> Dict[str, Any]:
        """使用持久连接执行db操作"""
        try:
            # 处理链式调用，如 db.test.find().limit(5) 或 db.test.find().explain()
            if ('.limit(' in query or '.skip(' in query or '.sort(' in query or
                '.explain(' in query):
                return await self._execute_chained_operation_persistent(query)

            # 首先检查是否是数据库级别的操作（如 db.createCollection）
            db_level_pattern = r'db\.(\w+)\((.*)\)'
            db_match = re.match(db_level_pattern, query)

            if db_match:
                operation = db_match.group(1)
                params = db_match.group(2)

                # 处理数据库级别的操作
                if operation == 'createCollection':
                    return await self._execute_create_collection_persistent(params)
                elif operation == 'dropDatabase':
                    return await self._execute_drop_database_persistent(params)
                elif operation == 'runCommand':
                    return await self._execute_run_command_persistent(params)
                else:
                    # 如果不是已知的数据库级别操作，继续尝试集合级别操作
                    pass

            # 提取集合名和操作
            pattern = r'db\.(\w+)\.(\w+)\((.*)\)'
            match = re.match(pattern, query)

            if not match:
                raise Exception(f"Invalid MongoDB query format: {query}")

            collection_name = match.group(1)
            operation = match.group(2)
            params = match.group(3)

            # 使用持久连接的数据库实例
            collection = self._persistent_db[collection_name]

            # 根据操作类型执行相应的方法
            if operation == 'find':
                return await self._execute_find(collection, params)
            elif operation == 'findOne':
                return await self._execute_find_one(collection, params)
            elif operation == 'insertOne':
                return await self._execute_insert_one(collection, params)
            elif operation == 'insertMany':
                return await self._execute_insert_many(collection, params)
            elif operation == 'updateOne':
                return await self._execute_update_one(collection, params)
            elif operation == 'updateMany':
                return await self._execute_update_many(collection, params)
            elif operation == 'deleteOne':
                return await self._execute_delete_one(collection, params)
            elif operation == 'deleteMany':
                return await self._execute_delete_many(collection, params)
            elif operation == 'countDocuments':
                return await self._execute_count_documents(collection, params)
            elif operation == 'distinct':
                return await self._execute_distinct(collection, params)
            elif operation == 'createIndex':
                return await self._execute_create_index(collection, params)
            elif operation == 'drop':
                return await self._execute_drop(collection, params)
            elif operation == 'getShardDistribution':
                return await self._execute_shard_distribution(collection, params)
            elif operation == 'stats':
                return await self._execute_collection_stats(collection, params)
            elif operation == 'explain':
                return await self._execute_explain(collection, params)
            else:
                raise Exception(f"Unsupported MongoDB operation: {operation}")

        except Exception as e:
            logger.error(f"Persistent db operation failed: {str(e)}")
            raise

    async def _execute_chained_operation_persistent(self, query: str) -> Dict[str, Any]:
        """使用持久连接执行链式操作"""
        try:
            # 解析基础查询和链式方法
            base_pattern = r'db\.(\w+)\.find\((.*?)\)'
            base_match = re.search(base_pattern, query)

            if not base_match:
                raise Exception(f"Invalid chained query format: {query}")

            collection_name = base_match.group(1)
            find_params = base_match.group(2)

            # 使用持久连接的数据库实例
            collection = self._persistent_db[collection_name]

            # 解析find参数
            if find_params.strip():
                filter_doc = self._parse_json_params(find_params)
            else:
                filter_doc = {}

            # 执行基础查询
            cursor = await asyncio.get_event_loop().run_in_executor(
                None, collection.find, filter_doc
            )

            # 解析链式方法
            chain_methods = []

            # 查找所有链式方法
            chain_pattern = r'\.(\w+)\((.*?)\)'
            chain_matches = re.findall(chain_pattern, query[base_match.end():])

            sort_spec = None
            skip_value = None
            limit_value = None

            for method_name, method_params in chain_matches:
                chain_methods.append((method_name, method_params))

                if method_name == 'sort':
                    if method_params.strip():
                        sort_spec = self._parse_json_params(method_params)
                elif method_name == 'skip':
                    skip_value = int(method_params) if method_params.strip() else 0
                elif method_name == 'limit':
                    limit_value = int(method_params) if method_params.strip() else 0

            # 应用链式操作到游标
            if sort_spec:
                cursor = cursor.sort(list(sort_spec.items()))
            if skip_value:
                cursor = cursor.skip(skip_value)
            if limit_value:
                cursor = cursor.limit(limit_value)

            # 转换结果
            documents = []
            for doc in cursor:
                # 转换ObjectId为字符串
                doc = self._convert_objectid_to_str(doc)
                documents.append(doc)

            return {
                'type': 'query',
                'operation': 'find',
                'collection': collection.name,
                'data': documents,
                'count': len(documents),
                'applied_chains': [f"{name}({params})" for name, params in chain_methods]
            }

        except Exception as e:
            logger.error(f"Persistent chained operation failed: {str(e)}")
            raise

    async def _execute_use_command_persistent(self, query: str) -> Dict[str, Any]:
        """使用持久连接执行use命令"""
        try:
            # 提取数据库名
            db_name = query.replace('use ', '').strip()
            self._persistent_db = self._persistent_client[db_name]

            return {
                'type': 'command',
                'operation': 'use',
                'message': f"Switched to database: {db_name} (persistent connection)"
            }

        except Exception as e:
            logger.error(f"Persistent use command failed: {str(e)}")
            raise

    async def _execute_show_command_persistent(self, query: str) -> Dict[str, Any]:
        """使用持久连接执行show命令"""
        try:
            if 'databases' in query or 'dbs' in query:
                databases = await self._get_databases_persistent()
                return {
                    'type': 'query',
                    'operation': 'show databases',
                    'data': [{'name': db} for db in databases],
                    'count': len(databases)
                }
            elif 'collections' in query:
                collections = await self._get_collections_persistent()
                return {
                    'type': 'query',
                    'operation': 'show collections',
                    'data': [{'name': coll} for coll in collections],
                    'count': len(collections)
                }
            else:
                raise Exception(f"Unsupported show command: {query}")

        except Exception as e:
            logger.error(f"Persistent show command failed: {str(e)}")
            raise

    async def _get_databases_persistent(self) -> List[str]:
        """使用持久连接获取数据库列表"""
        try:
            databases = await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_client.list_database_names
            )
            return databases
        except Exception as e:
            logger.error(f"Failed to get databases with persistent connection: {str(e)}")
            raise

    async def _get_collections_persistent(self) -> List[str]:
        """使用持久连接获取集合列表"""
        try:
            collections = await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_db.list_collection_names
            )
            return collections
        except Exception as e:
            logger.error(f"Failed to get collections with persistent connection: {str(e)}")
            raise

    async def _execute_create_index(self, collection, params: str) -> Dict[str, Any]:
        """执行createIndex操作"""
        try:
            # MongoDB createIndex语法: db.collection.createIndex({key: 1}, {unique: true})
            # 需要解析两个参数：索引键定义和索引选项
            
            # 移除外层括号
            params = params.strip()
            if params.startswith('(') and params.endswith(')'):
                params = params[1:-1]
            
            # 分割两个参数
            if ',' in params:
                # 找到第一个参数的结束位置（第一个完整的JSON对象）
                brace_count = 0
                first_param_end = -1
                in_string = False
                escape_next = False
                
                for i, char in enumerate(params):
                    if escape_next:
                        escape_next = False
                        continue
                    
                    if char == '\\':
                        escape_next = True
                        continue
                    
                    if char == '"' and not escape_next:
                        in_string = not in_string
                        continue
                    
                    if not in_string:
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                first_param_end = i + 1
                                break
                
                if first_param_end > 0:
                    keys_param = params[:first_param_end].strip()
                    options_param = params[first_param_end:].strip()
                    
                    # 移除options_param开头的逗号
                    if options_param.startswith(','):
                        options_param = options_param[1:].strip()
                    
                    # 解析索引键定义
                    keys = self._parse_json_params(keys_param)
                    
                    # 解析索引选项（如果有的话）
                    options = {}
                    if options_param and options_param.strip():
                        try:
                            options = self._parse_json_params(options_param)
                        except Exception as e:
                            logger.warning(f"Failed to parse index options '{options_param}': {e}")
                            # 如果选项解析失败，使用默认选项
                            options = {}
                else:
                    # 如果无法分割，尝试将整个参数作为索引键定义
                    keys = self._parse_json_params(params)
                    options = {}
            else:
                # 只有一个参数，作为索引键定义
                keys = self._parse_json_params(params)
                options = {}
            
            # 执行创建索引操作
            if options:
                # 如果有选项，将选项作为关键字参数传递
                # MongoDB Python驱动的create_index方法支持关键字参数
                result = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: collection.create_index(keys, **options)
                )
            else:
                # 如果没有选项，只传递索引键定义
                result = await asyncio.get_event_loop().run_in_executor(
                    None, collection.create_index, keys
                )

            return {
                'type': 'modification',
                'operation': 'createIndex',
                'collection': collection.name,
                'index_name': result,
                'keys': keys,
                'options': options,
                'message': f"Successfully created index: {result}"
            }

        except Exception as e:
            logger.error(f"Create index operation failed: {str(e)}")
            raise

    async def _execute_create_collection(self, params: str) -> Dict[str, Any]:
        """执行createCollection操作"""
        try:
            # 解析集合名称
            collection_name = params.strip().strip("'\"")

            # 如果没有数据库连接，返回模拟结果
            if not self.db:
                return {
                    'type': 'command',
                    'operation': 'createCollection',
                    'collection_name': collection_name,
                    'acknowledged': True,
                    'simulated': True
                }

            # 检查集合是否已存在
            existing_collections = await asyncio.get_event_loop().run_in_executor(
                None, self.db.list_collection_names
            )

            if collection_name in existing_collections:
                return {
                    'type': 'modification',
                    'operation': 'createCollection',
                    'collection': collection_name,
                    'message': f"Collection {collection_name} already exists",
                    'acknowledged': True,
                    'already_exists': True
                }

            # 创建集合
            await asyncio.get_event_loop().run_in_executor(
                None, self.db.create_collection, collection_name
            )

            return {
                'type': 'modification',
                'operation': 'createCollection',
                'collection': collection_name,
                'message': f"Successfully created collection: {collection_name}",
                'acknowledged': True,
                'already_exists': False
            }

        except Exception as e:
            logger.error(f"Create collection operation failed: {str(e)}")
            raise

    async def _execute_drop_database(self, params: str) -> Dict[str, Any]:
        """执行dropDatabase操作"""
        try:
            # dropDatabase通常不需要参数
            database_name = self.db.name

            # 删除数据库
            await asyncio.get_event_loop().run_in_executor(
                None, self.client.drop_database, database_name
            )

            return {
                'type': 'modification',
                'operation': 'dropDatabase',
                'database': database_name,
                'message': f"Successfully dropped database: {database_name}",
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Drop database operation failed: {str(e)}")
            raise

    async def _execute_run_command(self, params: str) -> Dict[str, Any]:
        """执行runCommand操作"""
        try:
            # 解析命令参数
            command = self._parse_json_params(params)

            # 执行命令
            result = await asyncio.get_event_loop().run_in_executor(
                None, self.db.command, command
            )

            return {
                'type': 'command',
                'operation': 'runCommand',
                'command': command,
                'result': result,
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Run command operation failed: {str(e)}")
            raise

    def _parse_base_operation_with_chains(self, query: str) -> tuple:
        """解析基础操作和剩余的链式调用部分"""
        # 查找db.collection.method的起始位置
        pattern = r'db\.\w+\.\w+\('
        match = re.search(pattern, query)

        if not match:
            raise Exception(f"Cannot find base operation in: {query}")

        start = match.start()
        paren_start = match.end() - 1  # 指向开括号
        paren_count = 1
        i = paren_start + 1

        # 找到匹配的闭括号
        while i < len(query) and paren_count > 0:
            if query[i] == '(':
                paren_count += 1
            elif query[i] == ')':
                paren_count -= 1
            i += 1

        if paren_count != 0:
            raise Exception(f"Unmatched parentheses in: {query}")

        base_operation = query[start:i]
        remaining_query = query[i:]

        return base_operation, remaining_query

    def _parse_chain_methods(self, remaining_query: str) -> list:
        """解析链式方法调用"""
        chain_methods = []

        # 查找所有的.method()调用
        pattern = r'\.(\w+)\(([^)]*)\)'
        matches = re.findall(pattern, remaining_query)

        for method_name, method_params in matches:
            chain_methods.append({
                'method': method_name,
                'params': method_params.strip()
            })

        return chain_methods

    async def _execute_create_collection_persistent(self, params: str) -> Dict[str, Any]:
        """使用持久连接执行createCollection操作"""
        try:
            # 解析集合名称
            collection_name = params.strip().strip("'\"")

            # 检查集合是否已存在
            existing_collections = await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_db.list_collection_names
            )

            if collection_name in existing_collections:
                return {
                    'type': 'modification',
                    'operation': 'createCollection',
                    'collection': collection_name,
                    'message': f"Collection {collection_name} already exists",
                    'acknowledged': True,
                    'already_exists': True
                }

            # 创建集合
            await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_db.create_collection, collection_name
            )

            return {
                'type': 'modification',
                'operation': 'createCollection',
                'collection': collection_name,
                'message': f"Successfully created collection: {collection_name}",
                'acknowledged': True,
                'already_exists': False
            }

        except Exception as e:
            logger.error(f"Create collection operation failed: {str(e)}")
            raise

    async def _execute_drop_database_persistent(self, params: str) -> Dict[str, Any]:
        """使用持久连接执行dropDatabase操作"""
        try:
            # dropDatabase通常不需要参数
            database_name = self._persistent_db.name

            # 删除数据库
            await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_client.drop_database, database_name
            )

            return {
                'type': 'modification',
                'operation': 'dropDatabase',
                'database': database_name,
                'message': f"Successfully dropped database: {database_name}",
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Drop database operation failed: {str(e)}")
            raise

    async def _execute_run_command_persistent(self, params: str) -> Dict[str, Any]:
        """使用持久连接执行runCommand操作"""
        try:
            # 解析命令参数
            command = self._parse_json_params(params)

            # 执行命令
            result = await asyncio.get_event_loop().run_in_executor(
                None, self._persistent_db.command, command
            )

            return {
                'type': 'command',
                'operation': 'runCommand',
                'command': command,
                'result': result,
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Run command operation failed: {str(e)}")
            raise

    async def _execute_create_view(self, params: str) -> Dict[str, Any]:
        """执行创建视图操作"""
        try:
            # 解析参数：viewName, source, pipeline
            # 格式：'viewName', 'source', [pipeline]
            import re

            # 简单的参数解析
            params_clean = params.strip()

            # 使用正则表达式解析参数
            # 匹配格式：'viewName', 'source', [pipeline]
            pattern = r"'([^']+)',\s*'([^']+)',\s*(\[.*\])"
            match = re.match(pattern, params_clean)

            if not match:
                raise Exception("Invalid createView parameters format")

            view_name = match.group(1)
            source_collection = match.group(2)
            pipeline_str = match.group(3)

            # 如果没有数据库连接，返回模拟结果
            if not self.db:
                return {
                    'type': 'command',
                    'operation': 'createView',
                    'view_name': view_name,
                    'source': source_collection,
                    'pipeline': pipeline_str,
                    'acknowledged': True,
                    'simulated': True
                }

            # 解析管道
            pipeline = self._parse_json_params(pipeline_str)

            # 创建视图
            await asyncio.get_event_loop().run_in_executor(
                None, self.db.create_collection, view_name,
                viewOn=source_collection, pipeline=pipeline
            )

            return {
                'type': 'command',
                'operation': 'createView',
                'view_name': view_name,
                'source': source_collection,
                'pipeline': pipeline,
                'acknowledged': True
            }

        except Exception as e:
            logger.error(f"Create view operation failed: {str(e)}")
            raise

    async def _execute_shard_operation(self, query: str) -> Dict[str, Any]:
        """执行分片操作"""
        try:
            # 解析分片操作
            if query.startswith('sh.enableSharding('):
                # 提取数据库名称
                pattern = r"sh\.enableSharding\('([^']+)'\)"
                match = re.match(pattern, query)
                if not match:
                    raise Exception("Invalid enableSharding format")

                db_name = match.group(1)

                # 如果没有客户端连接，返回模拟结果
                if not self.client:
                    return {
                        'type': 'command',
                        'operation': 'enableSharding',
                        'database': db_name,
                        'acknowledged': True,
                        'simulated': True
                    }

                # 执行enableSharding命令
                admin_db = self.client.admin
                result = await asyncio.get_event_loop().run_in_executor(
                    None, admin_db.command, {"enableSharding": db_name}
                )

                return {
                    'type': 'command',
                    'operation': 'enableSharding',
                    'database': db_name,
                    'result': result,
                    'acknowledged': True
                }

            elif query.startswith('sh.shardCollection('):
                # 提取集合名称和分片键
                pattern = r"sh\.shardCollection\('([^']+)',\s*(\{.*\})\)"
                match = re.match(pattern, query)
                if not match:
                    raise Exception("Invalid shardCollection format")

                collection_name = match.group(1)
                shard_key_str = match.group(2)

                # 如果没有客户端连接，返回模拟结果
                if not self.client:
                    return {
                        'type': 'command',
                        'operation': 'shardCollection',
                        'collection': collection_name,
                        'shard_key': shard_key_str,
                        'acknowledged': True,
                        'simulated': True
                    }

                shard_key = self._parse_json_params(shard_key_str)

                # 执行shardCollection命令
                admin_db = self.client.admin
                result = await asyncio.get_event_loop().run_in_executor(
                    None, admin_db.command, {
                        "shardCollection": collection_name,
                        "key": shard_key
                    }
                )

                return {
                    'type': 'command',
                    'operation': 'shardCollection',
                    'collection': collection_name,
                    'shard_key': shard_key,
                    'result': result,
                    'acknowledged': True
                }
            else:
                raise Exception(f"Unsupported shard operation: {query}")

        except Exception as e:
            logger.error(f"Shard operation failed: {str(e)}")
            raise

# 注意：不创建默认实例，避免启动时连接外部数据库
# mongo_service 实例应该在需要时动态创建
