"""
PostgreSQL异步抓包服务 - 提供PostgreSQL数据库的异步抓包功能
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from services.postgres_local_packet_capture_service import PostgresLocalPacketCaptureService
from services.postgres_service import PostgresService
from services.database_config_service import database_config_service

logger = logging.getLogger(__name__)

class PostgresAsyncCaptureService:
    """PostgreSQL异步抓包服务"""
    
    def __init__(self):
        """初始化PostgreSQL异步抓包服务"""
        self.packet_service = PostgresLocalPacketCaptureService()
        self.postgres_service = None
        self.current_capture_file = None
        self.is_capturing = False
        logger.info("PostgreSQL Async Capture Service initialized")
    
    async def configure_postgres_connection(self, config_id: int):
        """配置PostgreSQL连接"""
        try:
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")
            
            # 创建PostgreSQL服务实例
            self.postgres_service = PostgresService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )
            
            # 初始化连接
            await self.postgres_service.initialize()
            
            # 测试连接
            is_connected = await self.postgres_service.check_connection()
            if not is_connected:
                raise Exception("PostgreSQL database connection failed")
            
            logger.info(f"PostgreSQL connection configured: {config.host}:{config.port}")
            
        except Exception as e:
            logger.error(f"Failed to configure PostgreSQL connection: {str(e)}")
            raise
    
    async def execute_sql_with_async_capture(
        self,
        sql_query: str,
        config_id: int,
        capture_duration: int = 10,
        use_c_executor: bool = False
    ) -> Dict[str, Any]:
        """执行PostgreSQL SQL查询并进行异步抓包"""
        try:
            logger.info(f"Starting PostgreSQL async capture for SQL: {sql_query}")
            
            # 配置数据库连接
            await self.configure_postgres_connection(config_id)
            
            # 获取数据库配置
            config = await database_config_service.get_config(config_id)
            
            # 启动抓包
            capture_file = await self.packet_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            self.current_capture_file = capture_file
            self.is_capturing = True
            
            logger.info(f"PostgreSQL packet capture started: {capture_file}")
            
            # 等待抓包服务启动
            await asyncio.sleep(2)

            # 执行SQL查询
            if use_c_executor:
                # 使用C语言执行器
                from services.gaussdb_service_v2 import GaussDBServiceV2
                executor_service = GaussDBServiceV2(
                    host=config.host,
                    port=config.port,
                    user=config.user,
                    password=config.password,
                    database=config.database_name,
                    use_c_executor=True
                )
                await executor_service.initialize()

                try:
                    # 创建持久连接
                    connection_created = await executor_service.create_persistent_connection()
                    if not connection_created:
                        raise Exception("Failed to create persistent PostgreSQL C executor connection")

                    # 使用C执行器执行SQL
                    execution_result = await executor_service.execute_sql_query_with_persistent_connection(sql_query)

                finally:
                    # 确保关闭持久连接和连接池
                    await executor_service.close_persistent_connection()
                    executor_service.close_pool()
                    logger.info("PostgreSQL C执行器持久连接和连接池已清理")
            else:
                # 使用Python执行器
                execution_result = await self.postgres_service.execute_query(sql_query)

            # 等待数据包捕获
            await asyncio.sleep(capture_duration)
            
            # 停止抓包
            final_packet_file = await self.packet_service.stop_capture()
            self.is_capturing = False
            
            logger.info(f"PostgreSQL async capture completed: {final_packet_file}")
            
            return {
                "success": True,
                "sql_query": sql_query,
                "execution_result": execution_result,
                "packet_file": final_packet_file,
                "capture_duration": capture_duration,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"PostgreSQL async capture failed: {str(e)}")
            
            # 确保停止抓包
            if self.is_capturing:
                try:
                    await self.packet_service.stop_capture()
                    self.is_capturing = False
                except Exception as stop_error:
                    logger.error(f"停止PostgreSQL抓包失败: {type(stop_error).__name__}: {stop_error}")
                    # 即使停止失败也要重置状态
                    self.is_capturing = False
            
            return {
                "success": False,
                "sql_query": sql_query,
                "error": str(e),
                "packet_file": None,
                "timestamp": datetime.now().isoformat()
            }
    
    async def start_capture_session(self, config_id: int) -> Dict[str, Any]:
        """启动抓包会话"""
        try:
            if self.is_capturing:
                return {
                    "success": False,
                    "error": "Capture session already active",
                    "current_file": self.current_capture_file
                }
            
            # 获取数据库配置
            config = await database_config_service.get_config(config_id)
            
            # 启动抓包
            capture_file = await self.packet_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            self.current_capture_file = capture_file
            self.is_capturing = True
            
            logger.info(f"PostgreSQL capture session started: {capture_file}")
            
            return {
                "success": True,
                "capture_file": capture_file,
                "message": "PostgreSQL capture session started"
            }
            
        except Exception as e:
            logger.error(f"Failed to start PostgreSQL capture session: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def stop_capture_session(self) -> Dict[str, Any]:
        """停止抓包会话"""
        try:
            if not self.is_capturing:
                return {
                    "success": False,
                    "error": "No active capture session"
                }
            
            # 停止抓包
            final_packet_file = await self.packet_service.stop_capture()
            self.is_capturing = False
            self.current_capture_file = None
            
            logger.info(f"PostgreSQL capture session stopped: {final_packet_file}")
            
            return {
                "success": True,
                "packet_file": final_packet_file,
                "message": "PostgreSQL capture session stopped"
            }
            
        except Exception as e:
            logger.error(f"Failed to stop PostgreSQL capture session: {str(e)}")
            self.is_capturing = False
            self.current_capture_file = None
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_capture_status(self) -> Dict[str, Any]:
        """获取抓包状态"""
        return {
            "is_capturing": self.is_capturing,
            "current_file": self.current_capture_file,
            "service_status": "active"
        }

# 创建全局服务实例
postgres_async_capture_service = PostgresAsyncCaptureService()
