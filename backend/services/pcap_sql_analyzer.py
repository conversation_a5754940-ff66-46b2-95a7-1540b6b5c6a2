#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PCAP SQL分析器 - 用于检测抓包文件中的SQL语句
支持MySQL、PostgreSQL、MongoDB、Oracle、SQL Server五种数据库协议
"""

import os
import re
import json
import time
import struct
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseType(Enum):
    """数据库类型枚举"""
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    MONGODB = "mongodb"
    ORACLE = "oracle"
    SQLSERVER = "sqlserver"
    GAUSSDB = "gaussdb"

class SQLType(Enum):
    """SQL语句类型枚举"""
    SELECT = "SELECT"
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    CREATE = "CREATE"
    DROP = "DROP"
    ALTER = "ALTER"
    GRANT = "GRANT"
    REVOKE = "REVOKE"
    COMMIT = "COMMIT"
    ROLLBACK = "ROLLBACK"
    UNKNOWN = "UNKNOWN"

@dataclass
class SQLStatement:
    """SQL语句信息"""
    sql: str
    sql_type: SQLType
    database_type: DatabaseType
    timestamp: float
    packet_number: int
    source_ip: str
    dest_ip: str
    source_port: int
    dest_port: int
    confidence: float  # 置信度 0-1

@dataclass
class AnalysisResult:
    """分析结果"""
    pcap_file: str
    total_packets: int
    database_packets: int
    sql_statements: List[SQLStatement]
    analysis_time: float
    success: bool
    error_message: Optional[str] = None

class PCAPSQLAnalyzer:
    """PCAP SQL分析器主类"""
    
    def __init__(self):
        """初始化分析器"""
        self.mysql_analyzer = MySQLProtocolAnalyzer()
        self.postgresql_analyzer = PostgreSQLProtocolAnalyzer()
        self.mongodb_analyzer = MongoDBProtocolAnalyzer()
        self.oracle_analyzer = OracleProtocolAnalyzer()
        self.sqlserver_analyzer = SQLServerProtocolAnalyzer()
        
        # 增强的Oracle分析器（用于处理TNS协议分片）
        self.enhanced_oracle_analyzer = None
        
        # 默认数据库端口映射（作为后备）
        self.default_port_to_database = {
            3306: DatabaseType.MYSQL,
            5432: DatabaseType.POSTGRESQL,
            27017: DatabaseType.MONGODB,
            1521: DatabaseType.ORACLE,
            1433: DatabaseType.SQLSERVER,
            # 注意：GaussDB使用5432端口，但当明确指定database_type时会优先使用指定类型
        }

        # 动态端口映射（运行时设置）
        self.port_to_database = self.default_port_to_database.copy()

    def set_database_port_mapping(self, database_type: str, port: int):
        """动态设置数据库类型和端口的映射关系"""
        try:
            db_type = DatabaseType(database_type.lower())
            self.port_to_database[port] = db_type
            logger.info(f"设置端口映射: {port} -> {database_type}")
        except ValueError:
            logger.warning(f"不支持的数据库类型: {database_type}")

    def clear_port_mapping(self):
        """清除动态端口映射，恢复默认映射"""
        self.port_to_database = self.default_port_to_database.copy()
        logger.info("已恢复默认端口映射")

    def analyze_pcap_file(self, pcap_file: str, database_type: str = None, database_port: int = None) -> AnalysisResult:
        """分析PCAP文件中的SQL语句"""
        start_time = time.time()
        
        try:
            # 检查文件是否存在
            if not os.path.exists(pcap_file):
                return AnalysisResult(
                    pcap_file=pcap_file,
                    total_packets=0,
                    database_packets=0,
                    sql_statements=[],
                    analysis_time=0,
                    success=False,
                    error_message=f"文件不存在: {pcap_file}"
                )
            
            logger.info(f"开始分析PCAP文件: {pcap_file}")

            # 如果提供了数据库类型和端口，设置动态映射
            if database_type and database_port:
                self.set_database_port_mapping(database_type, database_port)
                logger.info(f"使用指定的数据库类型和端口: {database_type}:{database_port}")

            # 使用scapy读取pcap文件
            try:
                from scapy.all import rdpcap, TCP, IP
            except ImportError:
                return AnalysisResult(
                    pcap_file=pcap_file,
                    total_packets=0,
                    database_packets=0,
                    sql_statements=[],
                    analysis_time=0,
                    success=False,
                    error_message="scapy库未安装，请安装: pip install scapy"
                )
            
            # 读取数据包
            packets = rdpcap(pcap_file)
            total_packets = len(packets)
            database_packets = 0
            sql_statements = []
            
            logger.info(f"读取到 {total_packets} 个数据包")
            
            # 检查是否是Oracle协议，如果是则使用增强分析器
            if database_type and database_type.lower() == "oracle":
                # 使用增强的Oracle分析器处理TNS协议分片
                if self.enhanced_oracle_analyzer is None:
                    try:
                        from .oracle_protocol_analyzer import EnhancedOracleProtocolAnalyzer
                        self.enhanced_oracle_analyzer = EnhancedOracleProtocolAnalyzer()
                        logger.info("启用增强Oracle协议分析器")
                    except ImportError as e:
                        logger.warning(f"无法导入增强Oracle分析器: {e}")
                        self.enhanced_oracle_analyzer = None
                
                if self.enhanced_oracle_analyzer:
                    # 使用增强分析器分析整个文件
                    enhanced_statements = self.enhanced_oracle_analyzer.analyze_pcap_file(pcap_file, database_type, database_port)
                    if enhanced_statements:
                        sql_statements.extend(enhanced_statements)
                        logger.info(f"增强Oracle分析器找到 {len(enhanced_statements)} 个SQL语句")
                        # 跳过逐包分析
                        analysis_time = time.time() - start_time
                        return AnalysisResult(
                            pcap_file=pcap_file,
                            total_packets=total_packets,
                            database_packets=len(enhanced_statements),  # 使用找到的SQL语句数
                            sql_statements=sql_statements,
                            analysis_time=analysis_time,
                            success=True
                        )
            
            # 分析每个数据包（原有的逐包分析逻辑）
            for i, packet in enumerate(packets):
                if not (IP in packet and TCP in packet):
                    continue
                
                # 获取包信息
                ip_layer = packet[IP]
                tcp_layer = packet[TCP]
                
                source_ip = ip_layer.src
                dest_ip = ip_layer.dst
                source_port = tcp_layer.sport
                dest_port = tcp_layer.dport
                timestamp = packet.time
                
                # 判断是否是数据库相关的包
                database_type = self._identify_database_type(source_port, dest_port)
                if not database_type:
                    continue
                
                database_packets += 1
                
                # 提取TCP载荷
                if not tcp_layer.payload:
                    continue
                
                payload = bytes(tcp_layer.payload)
                if len(payload) < 5:  # 太短的包忽略
                    continue
                
                # 根据数据库类型分析SQL语句
                statements = self._analyze_payload(
                    payload, database_type, timestamp, i + 1,
                    source_ip, dest_ip, source_port, dest_port
                )
                
                sql_statements.extend(statements)
            
            analysis_time = time.time() - start_time
            
            logger.info(f"分析完成: 总包数={total_packets}, 数据库包数={database_packets}, SQL语句数={len(sql_statements)}")
            
            return AnalysisResult(
                pcap_file=pcap_file,
                total_packets=total_packets,
                database_packets=database_packets,
                sql_statements=sql_statements,
                analysis_time=analysis_time,
                success=True
            )
            
        except Exception as e:
            analysis_time = time.time() - start_time
            logger.error(f"分析PCAP文件失败: {str(e)}")
            return AnalysisResult(
                pcap_file=pcap_file,
                total_packets=0,
                database_packets=0,
                sql_statements=[],
                analysis_time=analysis_time,
                success=False,
                error_message=str(e)
            )
    
    def _identify_database_type(self, source_port: int, dest_port: int) -> Optional[DatabaseType]:
        """根据端口识别数据库类型"""
        for port in [source_port, dest_port]:
            if port in self.port_to_database:
                return self.port_to_database[port]
        return None
    
    def _analyze_payload(self, payload: bytes, database_type: DatabaseType, 
                        timestamp: float, packet_number: int,
                        source_ip: str, dest_ip: str, 
                        source_port: int, dest_port: int) -> List[SQLStatement]:
        """根据数据库类型分析载荷中的SQL语句"""
        statements = []
        
        try:
            if database_type == DatabaseType.MYSQL:
                statements = self.mysql_analyzer.analyze(payload, timestamp, packet_number, 
                                                       source_ip, dest_ip, source_port, dest_port)
            elif database_type == DatabaseType.POSTGRESQL:
                statements = self.postgresql_analyzer.analyze(payload, timestamp, packet_number,
                                                            source_ip, dest_ip, source_port, dest_port)
            elif database_type == DatabaseType.MONGODB:
                statements = self.mongodb_analyzer.analyze(payload, timestamp, packet_number,
                                                         source_ip, dest_ip, source_port, dest_port)
            elif database_type == DatabaseType.ORACLE:
                statements = self.oracle_analyzer.analyze(payload, timestamp, packet_number,
                                                        source_ip, dest_ip, source_port, dest_port)
            elif database_type == DatabaseType.SQLSERVER:
                statements = self.sqlserver_analyzer.analyze(payload, timestamp, packet_number,
                                                           source_ip, dest_ip, source_port, dest_port)
            elif database_type == DatabaseType.GAUSSDB:
                # GaussDB兼容PostgreSQL协议，使用PostgreSQL分析器
                statements = self.postgresql_analyzer.analyze(payload, timestamp, packet_number,
                                                            source_ip, dest_ip, source_port, dest_port)
                # 将数据库类型标记为GaussDB
                for stmt in statements:
                    stmt.database_type = DatabaseType.GAUSSDB
        except Exception as e:
            logger.warning(f"分析{database_type.value}协议载荷失败: {str(e)}")
        
        return statements
    
    def get_sql_statistics(self, result: AnalysisResult) -> Dict[str, Any]:
        """获取SQL语句统计信息"""
        if not result.success:
            return {"error": result.error_message}
        
        stats = {
            "total_sql_statements": len(result.sql_statements),
            "sql_types": {},
            "database_types": {},
            "confidence_distribution": {"high": 0, "medium": 0, "low": 0},
            "unique_sql_count": 0,
            "time_range": {"start": None, "end": None}
        }
        
        # 统计SQL类型
        for stmt in result.sql_statements:
            sql_type = stmt.sql_type.value
            db_type = stmt.database_type.value
            
            stats["sql_types"][sql_type] = stats["sql_types"].get(sql_type, 0) + 1
            stats["database_types"][db_type] = stats["database_types"].get(db_type, 0) + 1
            
            # 置信度分布
            if stmt.confidence >= 0.8:
                stats["confidence_distribution"]["high"] += 1
            elif stmt.confidence >= 0.5:
                stats["confidence_distribution"]["medium"] += 1
            else:
                stats["confidence_distribution"]["low"] += 1
        
        # 时间范围
        if result.sql_statements:
            timestamps = [stmt.timestamp for stmt in result.sql_statements]
            stats["time_range"]["start"] = min(timestamps)
            stats["time_range"]["end"] = max(timestamps)
        
        # 唯一SQL数量
        unique_sqls = set(stmt.sql.strip().upper() for stmt in result.sql_statements)
        stats["unique_sql_count"] = len(unique_sqls)
        
        return stats


# 基础协议分析器类
class BaseProtocolAnalyzer:
    """基础协议分析器"""
    
    def __init__(self, database_type: DatabaseType):
        self.database_type = database_type
    
    def analyze(self, payload: bytes, timestamp: float, packet_number: int,
                source_ip: str, dest_ip: str, source_port: int, dest_port: int) -> List[SQLStatement]:
        """分析协议载荷，子类需要实现此方法"""
        raise NotImplementedError
    
    def _classify_sql_type(self, sql: str) -> SQLType:
        """分类SQL语句类型 - 增强版"""
        sql_upper = sql.strip().upper()

        # 移除注释和多余空格
        sql_upper = re.sub(r'/\*.*?\*/', '', sql_upper, flags=re.DOTALL)
        sql_upper = re.sub(r'--.*$', '', sql_upper, flags=re.MULTILINE)
        sql_upper = re.sub(r'\s+', ' ', sql_upper).strip()

        if sql_upper.startswith('SELECT') or sql_upper.startswith('WITH'):
            return SQLType.SELECT
        elif sql_upper.startswith('INSERT'):
            return SQLType.INSERT
        elif sql_upper.startswith('UPDATE'):
            return SQLType.UPDATE
        elif sql_upper.startswith('DELETE'):
            return SQLType.DELETE
        elif sql_upper.startswith(('CREATE', 'CREATE TABLE', 'CREATE INDEX', 'CREATE DATABASE', 'CREATE SCHEMA')):
            return SQLType.CREATE
        elif sql_upper.startswith(('DROP', 'DROP TABLE', 'DROP INDEX', 'DROP DATABASE', 'DROP SCHEMA')):
            return SQLType.DROP
        elif sql_upper.startswith('ALTER'):
            return SQLType.ALTER
        elif sql_upper.startswith(('GRANT', 'REVOKE')):
            return SQLType.GRANT if sql_upper.startswith('GRANT') else SQLType.REVOKE
        elif sql_upper.startswith(('COMMIT', 'ROLLBACK')):
            return SQLType.COMMIT if sql_upper.startswith('COMMIT') else SQLType.ROLLBACK
        elif sql_upper.startswith(('BEGIN', 'START TRANSACTION')):
            return SQLType.COMMIT  # 事务开始也归类为事务操作
        elif sql_upper.startswith(('SHOW', 'DESCRIBE', 'DESC', 'EXPLAIN')):
            return SQLType.SELECT  # 查询类操作
        else:
            return SQLType.UNKNOWN
    
    def _clean_sql(self, sql: str) -> str:
        """清理SQL语句 - 增强版"""
        if not sql:
            return ""

        # 移除控制字符和不可打印字符
        sql = re.sub(r'[\x00-\x1f\x7f-\x9f]', ' ', sql)

        # 移除SQL注释
        sql = re.sub(r'/\*.*?\*/', '', sql, flags=re.DOTALL)
        sql = re.sub(r'--.*$', '', sql, flags=re.MULTILINE)

        # 压缩多个空格为单个空格
        sql = re.sub(r'\s+', ' ', sql)

        # 移除首尾空格和分号
        sql = sql.strip().rstrip(';')

        # 如果SQL太短或只包含空格，返回空字符串
        if len(sql.strip()) < 3:
            return ""

        return sql.strip()


# MySQL协议分析器
class MySQLProtocolAnalyzer(BaseProtocolAnalyzer):
    """MySQL协议分析器"""
    
    def __init__(self):
        super().__init__(DatabaseType.MYSQL)
    
    def analyze(self, payload: bytes, timestamp: float, packet_number: int,
                source_ip: str, dest_ip: str, source_port: int, dest_port: int) -> List[SQLStatement]:
        """分析MySQL协议载荷"""
        statements = []
        
        try:
            if len(payload) < 5:
                return statements
            
            # MySQL协议格式: [长度3字节][序号1字节][命令1字节][数据...]
            packet_length = struct.unpack('<I', payload[:3] + b'\x00')[0]
            sequence_id = payload[3]
            
            if len(payload) < 5:
                return statements
            
            command = payload[4]
            
            # COM_QUERY = 0x03
            if command == 0x03 and len(payload) > 5:
                try:
                    sql = payload[5:].decode('utf-8', errors='ignore')
                    sql = self._clean_sql(sql)
                    
                    if sql and len(sql) > 3:
                        sql_type = self._classify_sql_type(sql)
                        confidence = 0.9 if sql_type != SQLType.UNKNOWN else 0.6
                        
                        statements.append(SQLStatement(
                            sql=sql,
                            sql_type=sql_type,
                            database_type=self.database_type,
                            timestamp=timestamp,
                            packet_number=packet_number,
                            source_ip=source_ip,
                            dest_ip=dest_ip,
                            source_port=source_port,
                            dest_port=dest_port,
                            confidence=confidence
                        ))
                except UnicodeDecodeError:
                    pass
            
            # 也尝试在整个载荷中搜索SQL关键字 - 增强版
            try:
                payload_str = payload.decode('utf-8', errors='ignore')
                sql_patterns = [
                    # 基础SQL模式
                    r'\b(SELECT\s+.+?(?:FROM|;|\s*$))',
                    r'\b(INSERT\s+INTO\s+.+?(?:VALUES|SELECT|;|\s*$))',
                    r'\b(UPDATE\s+.+?(?:SET|;|\s*$))',
                    r'\b(DELETE\s+FROM\s+.+?(?:WHERE|;|\s*$))',
                    r'\b(CREATE\s+(?:TABLE|DATABASE|INDEX|SCHEMA|VIEW)\s+.+?(?:;|\s*$))',
                    r'\b(DROP\s+(?:TABLE|DATABASE|INDEX|SCHEMA|VIEW)\s+.+?(?:;|\s*$))',
                    r'\b(ALTER\s+TABLE\s+.+?(?:;|\s*$))',
                    # 扩展SQL模式
                    r'\b(SHOW\s+(?:TABLES|DATABASES|COLUMNS|INDEX).+?(?:;|\s*$))',
                    r'\b(DESCRIBE\s+\w+(?:;|\s*$))',
                    r'\b(EXPLAIN\s+.+?(?:;|\s*$))',
                    r'\b(WITH\s+.+?SELECT\s+.+?(?:;|\s*$))',
                    r'\b(BEGIN(?:\s+TRANSACTION)?(?:;|\s*$))',
                    r'\b(COMMIT(?:\s+TRANSACTION)?(?:;|\s*$))',
                    r'\b(ROLLBACK(?:\s+TRANSACTION)?(?:;|\s*$))',
                    r'\b(GRANT\s+.+?(?:;|\s*$))',
                    r'\b(REVOKE\s+.+?(?:;|\s*$))'
                ]
                
                for pattern in sql_patterns:
                    matches = re.finditer(pattern, payload_str, re.IGNORECASE | re.DOTALL)
                    for match in matches:
                        sql = self._clean_sql(match.group(1))
                        if sql and len(sql) > 10:  # 过滤太短的匹配
                            sql_type = self._classify_sql_type(sql)
                            confidence = 0.7
                            
                            # 避免重复添加
                            if not any(stmt.sql == sql for stmt in statements):
                                statements.append(SQLStatement(
                                    sql=sql,
                                    sql_type=sql_type,
                                    database_type=self.database_type,
                                    timestamp=timestamp,
                                    packet_number=packet_number,
                                    source_ip=source_ip,
                                    dest_ip=dest_ip,
                                    source_port=source_port,
                                    dest_port=dest_port,
                                    confidence=confidence
                                ))
            except UnicodeDecodeError:
                pass
                
        except Exception as e:
            logger.debug(f"MySQL协议分析错误: {str(e)}")
        
        return statements


# PostgreSQL协议分析器
class PostgreSQLProtocolAnalyzer(BaseProtocolAnalyzer):
    """PostgreSQL协议分析器"""

    def __init__(self):
        super().__init__(DatabaseType.POSTGRESQL)

    def analyze(self, payload: bytes, timestamp: float, packet_number: int,
                source_ip: str, dest_ip: str, source_port: int, dest_port: int) -> List[SQLStatement]:
        """分析PostgreSQL协议载荷"""
        statements = []

        try:
            if len(payload) < 5:
                return statements

            # PostgreSQL协议消息格式: [类型1字节][长度4字节][数据...]
            message_type = chr(payload[0]) if payload[0] < 128 else None

            # Query消息 (Q)
            if message_type == 'Q' and len(payload) > 5:
                try:
                    length = struct.unpack('>I', payload[1:5])[0]
                    if len(payload) >= length + 1:
                        sql = payload[5:length+1].decode('utf-8', errors='ignore').rstrip('\x00')
                        sql = self._clean_sql(sql)

                        if sql and len(sql) > 3:
                            sql_type = self._classify_sql_type(sql)
                            confidence = 0.95

                            statements.append(SQLStatement(
                                sql=sql,
                                sql_type=sql_type,
                                database_type=self.database_type,
                                timestamp=timestamp,
                                packet_number=packet_number,
                                source_ip=source_ip,
                                dest_ip=dest_ip,
                                source_port=source_port,
                                dest_port=dest_port,
                                confidence=confidence
                            ))
                except (UnicodeDecodeError, struct.error):
                    pass

            # Parse消息 (P) - Extended Query协议
            elif message_type == 'P' and len(payload) > 5:
                try:
                    length = struct.unpack('>I', payload[1:5])[0]
                    if len(payload) >= length + 1:
                        # 跳过statement name，找到SQL
                        data = payload[5:length+1]
                        null_pos = data.find(b'\x00')
                        if null_pos != -1 and null_pos + 1 < len(data):
                            sql = data[null_pos+1:].decode('utf-8', errors='ignore').rstrip('\x00')
                            sql = self._clean_sql(sql)

                            if sql and len(sql) > 3:
                                sql_type = self._classify_sql_type(sql)
                                confidence = 0.9

                                statements.append(SQLStatement(
                                    sql=sql,
                                    sql_type=sql_type,
                                    database_type=self.database_type,
                                    timestamp=timestamp,
                                    packet_number=packet_number,
                                    source_ip=source_ip,
                                    dest_ip=dest_ip,
                                    source_port=source_port,
                                    dest_port=dest_port,
                                    confidence=confidence
                                ))
                except (UnicodeDecodeError, struct.error):
                    pass

            # 通用SQL模式匹配 - PostgreSQL增强版
            try:
                payload_str = payload.decode('utf-8', errors='ignore')
                sql_patterns = [
                    # 基础SQL模式
                    r'\b(SELECT\s+.+?(?:FROM|;|\s*$))',
                    r'\b(INSERT\s+INTO\s+.+?(?:VALUES|SELECT|;|\s*$))',
                    r'\b(UPDATE\s+.+?(?:SET|;|\s*$))',
                    r'\b(DELETE\s+FROM\s+.+?(?:WHERE|;|\s*$))',
                    r'\b(CREATE\s+(?:TABLE|DATABASE|INDEX|SCHEMA|VIEW|FUNCTION|PROCEDURE)\s+.+?(?:;|\s*$))',
                    r'\b(DROP\s+(?:TABLE|DATABASE|INDEX|SCHEMA|VIEW|FUNCTION|PROCEDURE)\s+.+?(?:;|\s*$))',
                    r'\b(ALTER\s+TABLE\s+.+?(?:;|\s*$))',
                    # PostgreSQL特有模式
                    r'\b(COPY\s+.+?(?:FROM|TO).+?(?:;|\s*$))',
                    r'\b(VACUUM\s+.+?(?:;|\s*$))',
                    r'\b(ANALYZE\s+.+?(?:;|\s*$))',
                    r'\b(REINDEX\s+.+?(?:;|\s*$))',
                    r'\b(CLUSTER\s+.+?(?:;|\s*$))',
                    # 通用扩展模式
                    r'\b(WITH\s+.+?SELECT\s+.+?(?:;|\s*$))',
                    r'\b(BEGIN(?:\s+TRANSACTION)?(?:;|\s*$))',
                    r'\b(COMMIT(?:\s+TRANSACTION)?(?:;|\s*$))',
                    r'\b(ROLLBACK(?:\s+TRANSACTION)?(?:;|\s*$))'
                ]

                for pattern in sql_patterns:
                    matches = re.finditer(pattern, payload_str, re.IGNORECASE | re.DOTALL)
                    for match in matches:
                        sql = self._clean_sql(match.group(1))
                        if sql and len(sql) > 10:
                            sql_type = self._classify_sql_type(sql)
                            confidence = 0.7

                            if not any(stmt.sql == sql for stmt in statements):
                                statements.append(SQLStatement(
                                    sql=sql,
                                    sql_type=sql_type,
                                    database_type=self.database_type,
                                    timestamp=timestamp,
                                    packet_number=packet_number,
                                    source_ip=source_ip,
                                    dest_ip=dest_ip,
                                    source_port=source_port,
                                    dest_port=dest_port,
                                    confidence=confidence
                                ))
            except UnicodeDecodeError:
                pass

        except Exception as e:
            logger.debug(f"PostgreSQL协议分析错误: {str(e)}")

        return statements


# MongoDB协议分析器
class MongoDBProtocolAnalyzer(BaseProtocolAnalyzer):
    """MongoDB协议分析器"""

    def __init__(self):
        super().__init__(DatabaseType.MONGODB)

    def analyze(self, payload: bytes, timestamp: float, packet_number: int,
                source_ip: str, dest_ip: str, source_port: int, dest_port: int) -> List[SQLStatement]:
        """分析MongoDB协议载荷"""
        statements = []

        try:
            if len(payload) < 16:  # MongoDB消息头至少16字节
                return statements

            # MongoDB Wire Protocol消息头: [长度4字节][请求ID4字节][响应ID4字节][操作码4字节]
            message_length = struct.unpack('<I', payload[:4])[0]
            request_id = struct.unpack('<I', payload[4:8])[0]
            response_to = struct.unpack('<I', payload[8:12])[0]
            opcode = struct.unpack('<I', payload[12:16])[0]

            # OP_QUERY = 2004, OP_MSG = 2013
            if opcode in [2004, 2013] and len(payload) > 16:
                try:
                    # 尝试解析BSON文档中的查询
                    payload_str = payload[16:].decode('utf-8', errors='ignore')

                    # MongoDB查询模式
                    mongo_patterns = [
                        r'db\.(\w+)\.find\s*\([^)]*\)',
                        r'db\.(\w+)\.insertOne\s*\([^)]*\)',
                        r'db\.(\w+)\.insertMany\s*\([^)]*\)',
                        r'db\.(\w+)\.updateOne\s*\([^)]*\)',
                        r'db\.(\w+)\.updateMany\s*\([^)]*\)',
                        r'db\.(\w+)\.deleteOne\s*\([^)]*\)',
                        r'db\.(\w+)\.deleteMany\s*\([^)]*\)',
                        r'db\.(\w+)\.aggregate\s*\([^)]*\)',
                        r'db\.(\w+)\.count\s*\([^)]*\)',
                        r'"find"\s*:\s*"(\w+)"',
                        r'"insert"\s*:\s*"(\w+)"',
                        r'"update"\s*:\s*"(\w+)"',
                        r'"delete"\s*:\s*"(\w+)"'
                    ]

                    for pattern in mongo_patterns:
                        matches = re.finditer(pattern, payload_str, re.IGNORECASE)
                        for match in matches:
                            query = match.group(0)
                            query = self._clean_sql(query)

                            if query and len(query) > 5:
                                sql_type = self._classify_mongo_operation(query)
                                confidence = 0.8

                                statements.append(SQLStatement(
                                    sql=query,
                                    sql_type=sql_type,
                                    database_type=self.database_type,
                                    timestamp=timestamp,
                                    packet_number=packet_number,
                                    source_ip=source_ip,
                                    dest_ip=dest_ip,
                                    source_port=source_port,
                                    dest_port=dest_port,
                                    confidence=confidence
                                ))

                except UnicodeDecodeError:
                    pass

        except Exception as e:
            logger.debug(f"MongoDB协议分析错误: {str(e)}")

        return statements

    def _classify_mongo_operation(self, query: str) -> SQLType:
        """分类MongoDB操作类型"""
        query_lower = query.lower()

        if any(op in query_lower for op in ['find', 'count', 'aggregate']):
            return SQLType.SELECT
        elif any(op in query_lower for op in ['insert', '"insert"']):
            return SQLType.INSERT
        elif any(op in query_lower for op in ['update', '"update"']):
            return SQLType.UPDATE
        elif any(op in query_lower for op in ['delete', '"delete"']):
            return SQLType.DELETE
        elif any(op in query_lower for op in ['create', 'drop']):
            return SQLType.CREATE if 'create' in query_lower else SQLType.DROP
        else:
            return SQLType.UNKNOWN


# Oracle协议分析器
class OracleProtocolAnalyzer(BaseProtocolAnalyzer):
    """Oracle协议分析器"""

    def __init__(self):
        super().__init__(DatabaseType.ORACLE)

    def analyze(self, payload: bytes, timestamp: float, packet_number: int,
                source_ip: str, dest_ip: str, source_port: int, dest_port: int) -> List[SQLStatement]:
        """分析Oracle协议载荷"""
        statements = []

        try:
            if len(payload) < 8:
                return statements

            # Oracle TNS协议分析
            # TNS包头: [长度2字节][校验和2字节][类型1字节][保留1字节][头校验2字节]
            try:
                packet_length = struct.unpack('>H', payload[:2])[0]
                packet_checksum = struct.unpack('>H', payload[2:4])[0]
                packet_type = payload[4]

                # TNS Data包 (type = 6)
                if packet_type == 6 and len(payload) > 8:
                    # 尝试在数据部分查找SQL
                    data_part = payload[8:]
                    payload_str = data_part.decode('utf-8', errors='ignore')

                    # Oracle SQL模式 - 增强版
                    sql_patterns = [
                        # 基础SQL模式
                        r'\b(SELECT\s+.+?(?:FROM|;|\s*$))',
                        r'\b(INSERT\s+INTO\s+.+?(?:VALUES|SELECT|;|\s*$))',
                        r'\b(UPDATE\s+.+?(?:SET|;|\s*$))',
                        r'\b(DELETE\s+FROM\s+.+?(?:WHERE|;|\s*$))',
                        r'\b(CREATE\s+(?:TABLE|SEQUENCE|INDEX|VIEW|PROCEDURE|FUNCTION|PACKAGE)\s+.+?(?:;|\s*$))',
                        r'\b(DROP\s+(?:TABLE|SEQUENCE|INDEX|VIEW|PROCEDURE|FUNCTION|PACKAGE)\s+.+?(?:;|\s*$))',
                        r'\b(ALTER\s+TABLE\s+.+?(?:;|\s*$))',
                        # Oracle特有模式
                        r'\b(BEGIN\s+.+?END\s*;)',
                        r'\b(DECLARE\s+.+?BEGIN\s+.+?END\s*;)',
                        r'\b(MERGE\s+INTO\s+.+?(?:;|\s*$))',
                        r'\b(TRUNCATE\s+TABLE\s+.+?(?:;|\s*$))',
                        r'\b(GRANT\s+.+?(?:TO|;|\s*$))',
                        r'\b(REVOKE\s+.+?(?:FROM|;|\s*$))',
                        # PL/SQL块
                        r'(DECLARE\s+.+?BEGIN\s+.+?END\s*/)',
                        r'(BEGIN\s+.+?END\s*/)'
                    ]

                    for pattern in sql_patterns:
                        matches = re.finditer(pattern, payload_str, re.IGNORECASE | re.DOTALL)
                        for match in matches:
                            sql = self._clean_sql(match.group(1))
                            if sql and len(sql) > 10:
                                sql_type = self._classify_sql_type(sql)
                                confidence = 0.8

                                statements.append(SQLStatement(
                                    sql=sql,
                                    sql_type=sql_type,
                                    database_type=self.database_type,
                                    timestamp=timestamp,
                                    packet_number=packet_number,
                                    source_ip=source_ip,
                                    dest_ip=dest_ip,
                                    source_port=source_port,
                                    dest_port=dest_port,
                                    confidence=confidence
                                ))

            except (struct.error, UnicodeDecodeError):
                # 如果TNS解析失败，尝试通用SQL模式匹配
                try:
                    payload_str = payload.decode('utf-8', errors='ignore')
                    sql_patterns = [
                        r'\b(SELECT\s+.+?(?:FROM|;|$))',
                        r'\b(INSERT\s+INTO\s+.+?(?:VALUES|;|$))',
                        r'\b(UPDATE\s+.+?(?:SET|;|$))',
                        r'\b(DELETE\s+FROM\s+.+?(?:WHERE|;|$))'
                    ]

                    for pattern in sql_patterns:
                        matches = re.finditer(pattern, payload_str, re.IGNORECASE | re.DOTALL)
                        for match in matches:
                            sql = self._clean_sql(match.group(1))
                            if sql and len(sql) > 10:
                                sql_type = self._classify_sql_type(sql)
                                confidence = 0.6

                                if not any(stmt.sql == sql for stmt in statements):
                                    statements.append(SQLStatement(
                                        sql=sql,
                                        sql_type=sql_type,
                                        database_type=self.database_type,
                                        timestamp=timestamp,
                                        packet_number=packet_number,
                                        source_ip=source_ip,
                                        dest_ip=dest_ip,
                                        source_port=source_port,
                                        dest_port=dest_port,
                                        confidence=confidence
                                    ))
                except UnicodeDecodeError:
                    pass

        except Exception as e:
            logger.debug(f"Oracle协议分析错误: {str(e)}")

        return statements


# SQL Server协议分析器
class SQLServerProtocolAnalyzer(BaseProtocolAnalyzer):
    """SQL Server协议分析器"""

    def __init__(self):
        super().__init__(DatabaseType.SQLSERVER)

    def analyze(self, payload: bytes, timestamp: float, packet_number: int,
                source_ip: str, dest_ip: str, source_port: int, dest_port: int) -> List[SQLStatement]:
        """分析SQL Server协议载荷"""
        statements = []

        try:
            if len(payload) < 8:
                return statements

            # TDS (Tabular Data Stream) 协议分析
            # TDS包头: [类型1字节][状态1字节][长度2字节][SPID2字节][包ID1字节][窗口1字节]
            try:
                packet_type = payload[0]
                status = payload[1]
                length = struct.unpack('>H', payload[2:4])[0]
                spid = struct.unpack('>H', payload[4:6])[0]
                packet_id = payload[6]
                window = payload[7]

                # SQL Batch (type = 1) 或 RPC (type = 3)
                if packet_type in [1, 3] and len(payload) > 8:
                    # 尝试在数据部分查找SQL
                    data_part = payload[8:]

                    # SQL Server可能使用Unicode编码
                    try:
                        # 尝试UTF-16LE解码
                        payload_str = data_part.decode('utf-16le', errors='ignore')
                    except UnicodeDecodeError:
                        try:
                            # 回退到UTF-8
                            payload_str = data_part.decode('utf-8', errors='ignore')
                        except UnicodeDecodeError:
                            payload_str = data_part.decode('latin-1', errors='ignore')

                    # SQL Server SQL模式
                    sql_patterns = [
                        r'\b(SELECT\s+.+?(?:FROM|;|$))',
                        r'\b(INSERT\s+INTO\s+.+?(?:VALUES|;|$))',
                        r'\b(UPDATE\s+.+?(?:SET|;|$))',
                        r'\b(DELETE\s+FROM\s+.+?(?:WHERE|;|$))',
                        r'\b(CREATE\s+(?:TABLE|DATABASE|INDEX).+?(?:;|$))',
                        r'\b(DROP\s+(?:TABLE|DATABASE|INDEX).+?(?:;|$))',
                        r'\b(ALTER\s+TABLE\s+.+?(?:;|$))',
                        r'\b(EXEC\s+.+?(?:;|$))',
                        r'\b(EXECUTE\s+.+?(?:;|$))'
                    ]

                    for pattern in sql_patterns:
                        matches = re.finditer(pattern, payload_str, re.IGNORECASE | re.DOTALL)
                        for match in matches:
                            sql = self._clean_sql(match.group(1))
                            if sql and len(sql) > 10:
                                sql_type = self._classify_sql_type(sql)
                                confidence = 0.8

                                statements.append(SQLStatement(
                                    sql=sql,
                                    sql_type=sql_type,
                                    database_type=self.database_type,
                                    timestamp=timestamp,
                                    packet_number=packet_number,
                                    source_ip=source_ip,
                                    dest_ip=dest_ip,
                                    source_port=source_port,
                                    dest_port=dest_port,
                                    confidence=confidence
                                ))

            except (struct.error, UnicodeDecodeError):
                # 如果TDS解析失败，尝试通用SQL模式匹配
                try:
                    payload_str = payload.decode('utf-8', errors='ignore')
                    sql_patterns = [
                        r'\b(SELECT\s+.+?(?:FROM|;|$))',
                        r'\b(INSERT\s+INTO\s+.+?(?:VALUES|;|$))',
                        r'\b(UPDATE\s+.+?(?:SET|;|$))',
                        r'\b(DELETE\s+FROM\s+.+?(?:WHERE|;|$))'
                    ]

                    for pattern in sql_patterns:
                        matches = re.finditer(pattern, payload_str, re.IGNORECASE | re.DOTALL)
                        for match in matches:
                            sql = self._clean_sql(match.group(1))
                            if sql and len(sql) > 10:
                                sql_type = self._classify_sql_type(sql)
                                confidence = 0.6

                                if not any(stmt.sql == sql for stmt in statements):
                                    statements.append(SQLStatement(
                                        sql=sql,
                                        sql_type=sql_type,
                                        database_type=self.database_type,
                                        timestamp=timestamp,
                                        packet_number=packet_number,
                                        source_ip=source_ip,
                                        dest_ip=dest_ip,
                                        source_port=source_port,
                                        dest_port=dest_port,
                                        confidence=confidence
                                    ))
                except UnicodeDecodeError:
                    pass

        except Exception as e:
            logger.debug(f"SQL Server协议分析错误: {str(e)}")

        return statements


# 主函数和工具函数
def analyze_pcap_files(pcap_files: List[str]) -> List[AnalysisResult]:
    """批量分析多个PCAP文件"""
    analyzer = PCAPSQLAnalyzer()
    results = []

    for pcap_file in pcap_files:
        result = analyzer.analyze_pcap_file(pcap_file)
        results.append(result)

    return results


def generate_analysis_report(results: List[AnalysisResult], output_file: str = None) -> Dict[str, Any]:
    """生成分析报告"""
    report = {
        "summary": {
            "total_files": len(results),
            "successful_analyses": sum(1 for r in results if r.success),
            "failed_analyses": sum(1 for r in results if not r.success),
            "total_sql_statements": sum(len(r.sql_statements) for r in results if r.success),
            "total_packets": sum(r.total_packets for r in results if r.success),
            "total_database_packets": sum(r.database_packets for r in results if r.success)
        },
        "files": [],
        "sql_statistics": {
            "by_type": {},
            "by_database": {},
            "confidence_distribution": {"high": 0, "medium": 0, "low": 0}
        }
    }

    # 处理每个文件的结果
    for result in results:
        file_info = {
            "file": result.pcap_file,
            "success": result.success,
            "total_packets": result.total_packets,
            "database_packets": result.database_packets,
            "sql_count": len(result.sql_statements) if result.success else 0,
            "analysis_time": result.analysis_time,
            "error": result.error_message if not result.success else None
        }

        if result.success:
            analyzer = PCAPSQLAnalyzer()
            file_stats = analyzer.get_sql_statistics(result)
            file_info["statistics"] = file_stats

            # 汇总统计
            for sql_type, count in file_stats.get("sql_types", {}).items():
                report["sql_statistics"]["by_type"][sql_type] = \
                    report["sql_statistics"]["by_type"].get(sql_type, 0) + count

            for db_type, count in file_stats.get("database_types", {}).items():
                report["sql_statistics"]["by_database"][db_type] = \
                    report["sql_statistics"]["by_database"].get(db_type, 0) + count

            for conf_level, count in file_stats.get("confidence_distribution", {}).items():
                report["sql_statistics"]["confidence_distribution"][conf_level] += count

        report["files"].append(file_info)

    # 保存报告到文件
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            logger.info(f"分析报告已保存到: {output_file}")
        except Exception as e:
            logger.error(f"保存报告失败: {str(e)}")

    return report


if __name__ == "__main__":
    # 命令行工具示例
    import sys
    import argparse

    parser = argparse.ArgumentParser(description="PCAP SQL分析器")
    parser.add_argument("pcap_files", nargs="+", help="要分析的PCAP文件路径")
    parser.add_argument("-o", "--output", help="输出报告文件路径")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 分析文件
    results = analyze_pcap_files(args.pcap_files)

    # 生成报告
    report = generate_analysis_report(results, args.output)

    # 记录摘要到日志
    logger.info(f"分析完成:")
    logger.info(f"  总文件数: {report['summary']['total_files']}")
    logger.info(f"  成功分析: {report['summary']['successful_analyses']}")
    logger.info(f"  分析失败: {report['summary']['failed_analyses']}")
    logger.info(f"  总SQL语句数: {report['summary']['total_sql_statements']}")
    logger.info(f"  总数据包数: {report['summary']['total_packets']}")
    logger.info(f"  数据库相关包数: {report['summary']['total_database_packets']}")

    if report['summary']['total_sql_statements'] > 0:
        logger.info(f"SQL类型分布:")
        for sql_type, count in report['sql_statistics']['by_type'].items():
            logger.info(f"  {sql_type}: {count}")

        logger.info(f"数据库类型分布:")
        for db_type, count in report['sql_statistics']['by_database'].items():
            logger.info(f"  {db_type}: {count}")

        logger.info(f"置信度分布:")
        conf_dist = report['sql_statistics']['confidence_distribution']
        logger.info(f"  高 (>=0.8): {conf_dist['high']}")
        logger.info(f"  中 (0.5-0.8): {conf_dist['medium']}")
        logger.info(f"  低 (<0.5): {conf_dist['low']}")
