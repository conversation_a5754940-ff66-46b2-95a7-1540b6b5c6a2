"""
MongoDB异步抓包服务 - 提供MongoDB数据库的异步抓包功能
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from services.mongo_local_packet_capture_service import MongoLocalPacketCaptureService
from services.mongo_service import MongoService
from services.database_config_service import database_config_service

logger = logging.getLogger(__name__)

class MongoAsyncCaptureService:
    """MongoDB异步抓包服务"""
    
    def __init__(self):
        """初始化MongoDB异步抓包服务"""
        self.packet_service = MongoLocalPacketCaptureService()
        self.mongo_service = None
        self.current_capture_file = None
        self.is_capturing = False
        logger.info("MongoDB Async Capture Service initialized")
    
    async def configure_mongo_connection(self, config_id: int):
        """配置MongoDB连接"""
        try:
            config = await database_config_service.get_config(config_id)
            if not config:
                raise Exception(f"Database config not found: {config_id}")
            
            # 创建MongoDB服务实例
            self.mongo_service = MongoService(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database_name
            )
            
            # 测试连接
            is_connected = await self.mongo_service.check_connection()
            if not is_connected:
                raise Exception("MongoDB database connection failed")
            
            logger.info(f"MongoDB connection configured: {config.host}:{config.port}")
            
        except Exception as e:
            logger.error(f"Failed to configure MongoDB connection: {str(e)}")
            raise
    
    async def execute_mongo_with_async_capture(
        self,
        mongo_query: str,
        config_id: int,
        capture_duration: int = 10,
        executor_type: str = "python"
    ) -> Dict[str, Any]:
        """执行MongoDB查询并进行异步抓包"""
        try:
            logger.info(f"Starting MongoDB async capture for query: {mongo_query} with {executor_type} executor")

            # 配置数据库连接
            await self.configure_mongo_connection(config_id)

            # 获取数据库配置
            config = await database_config_service.get_config(config_id)

            # 启动抓包
            capture_file = await self.packet_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            self.current_capture_file = capture_file
            self.is_capturing = True

            logger.info(f"MongoDB packet capture started: {capture_file}")

            # 等待抓包服务启动
            await asyncio.sleep(2)

            # 在开始查询前，确保清理任何旧的连接
            if executor_type == "c":
                # 对于C执行器，先清理可能存在的旧连接
                try:
                    from services.mongo_c_executor_service import mongo_c_executor_service
                    await mongo_c_executor_service.close_persistent_connection()
                    logger.info("Cleaned up any existing persistent connections before starting capture")
                except Exception as e:
                    logger.debug(f"No existing connections to clean up: {e}")

                # 等待连接完全关闭
                await asyncio.sleep(1)

            # 根据执行器类型执行MongoDB查询
            execution_result = None
            if executor_type == "c":
                # C执行器使用持久连接，需要更长的等待时间确保抓包捕获到连接建立过程
                logger.info("Using C executor, waiting additional time for packet capture to stabilize")
                await asyncio.sleep(2)  # 减少等待时间，因为已经清理了旧连接

                # 预处理MongoDB查询，将复杂的JavaScript for循环转换为C执行器能理解的格式
                processed_queries = self._preprocess_mongo_query_for_c_executor(mongo_query)

                # 使用C语言执行器
                from services.mongo_c_executor_service import mongo_c_executor_service
                if mongo_c_executor_service.is_available():
                    try:
                        # 创建持久连接
                        connection_created = await mongo_c_executor_service.create_persistent_connection(
                            config.host, config.port, config.user, config.password, config.database_name
                        )
                        if not connection_created:
                            raise Exception("Failed to create persistent MongoDB C executor connection")

                        # 执行预处理后的查询
                        if len(processed_queries) == 1:
                            # 单个查询
                            execution_result = await mongo_c_executor_service.execute_mongo_query_persistent(processed_queries[0])
                        else:
                            # 多个查询，需要逐个执行
                            execution_result = await self._execute_multiple_queries_with_c_executor(
                                mongo_c_executor_service, processed_queries
                            )
                        logger.info("MongoDB executed successfully with C executor")

                    except Exception as e:
                        logger.warning(f"C executor failed, falling back to Python: {e}")
                        execution_result = await self.mongo_service.execute_mongo_query(mongo_query)

                    finally:
                        # 确保关闭持久连接，产生网络流量（参考PostgreSQL模式）
                        try:
                            await mongo_c_executor_service.close_persistent_connection()
                            logger.info("MongoDB C执行器持久连接已关闭")
                        except Exception as e:
                            logger.warning(f"Failed to close MongoDB C executor connection: {e}")
                else:
                    logger.warning("C executor not available, using Python executor")
                    execution_result = await self.mongo_service.execute_mongo_query(mongo_query)
            else:
                # 使用Python执行器，也采用持久连接模式以避免旧连接干扰
                logger.info("Using Python executor with persistent connection mode")

                # 预处理MongoDB查询，处理复杂的JavaScript for循环
                processed_queries = self._preprocess_mongo_query_for_python_executor(mongo_query)

                try:
                    # 先关闭可能存在的旧连接
                    await self.mongo_service.close_persistent_connection()
                    logger.info("Cleaned up any existing persistent connections before starting capture")

                    # 等待连接完全关闭
                    await asyncio.sleep(1)

                    # 创建新的持久连接
                    connection_created = await self.mongo_service.create_persistent_connection()
                    if not connection_created:
                        logger.warning("Failed to create persistent connection, falling back to regular connection")
                        # 对于回退情况，执行预处理后的查询
                        if len(processed_queries) == 1:
                            execution_result = await self.mongo_service.execute_mongo_query(processed_queries[0])
                        else:
                            execution_result = await self._execute_multiple_queries_with_python_executor(processed_queries)
                    else:
                        # 使用持久连接执行预处理后的查询
                        if len(processed_queries) == 1:
                            execution_result = await self.mongo_service.execute_mongo_query_with_persistent_connection(processed_queries[0])
                        else:
                            execution_result = await self._execute_multiple_queries_with_python_persistent_connection(processed_queries)
                        logger.info("MongoDB executed successfully with Python persistent connection")

                        # 确保关闭持久连接，产生网络流量
                        try:
                            await self.mongo_service.close_persistent_connection()
                            logger.info("MongoDB Python持久连接已关闭")
                        except Exception as e:
                            logger.warning(f"Failed to close MongoDB Python persistent connection: {e}")

                except Exception as e:
                    logger.warning(f"Python persistent connection failed, falling back to regular connection: {e}")
                    # 对于异常回退情况，也执行预处理后的查询
                    if len(processed_queries) == 1:
                        execution_result = await self.mongo_service.execute_mongo_query(processed_queries[0])
                    else:
                        execution_result = await self._execute_multiple_queries_with_python_executor(processed_queries)

            # 等待数据包捕获
            await asyncio.sleep(capture_duration)

            # 停止抓包
            final_packet_file = await self.packet_service.stop_capture()
            self.is_capturing = False

            # 检查抓包是否成功
            if not final_packet_file:
                error_msg = "MongoDB抓包失败：未生成有效的抓包文件。这可能表明数据库连接没有通过监控的网络接口，或者抓包过滤器与实际流量不匹配。"
                logger.error(error_msg)
                raise Exception(error_msg)

            logger.info(f"MongoDB async capture completed: {final_packet_file}")

            return {
                "success": True,
                "mongo_query": mongo_query,
                "execution_result": execution_result,
                "packet_file": final_packet_file,
                "capture_duration": capture_duration,
                "executor_type": executor_type,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"MongoDB async capture failed: {str(e)}")

            # 确保停止抓包
            if self.is_capturing:
                try:
                    await self.packet_service.stop_capture()
                    self.is_capturing = False
                except Exception as stop_error:
                    logger.error(f"停止MongoDB抓包失败: {type(stop_error).__name__}: {stop_error}")
                    # 即使停止失败也要重置状态
                    self.is_capturing = False

            return {
                "success": False,
                "mongo_query": mongo_query,
                "execution_result": None,  # 添加这个字段以保持一致性
                "error": str(e),
                "packet_file": None,
                "timestamp": datetime.now().isoformat()
            }
    
    async def start_capture_session(self, config_id: int) -> Dict[str, Any]:
        """启动抓包会话"""
        try:
            if self.is_capturing:
                return {
                    "success": False,
                    "error": "Capture session already active",
                    "current_file": self.current_capture_file
                }
            
            # 获取数据库配置
            config = await database_config_service.get_config(config_id)
            
            # 启动抓包
            capture_file = await self.packet_service.start_capture(
                target_host=config.host,
                target_port=config.port
            )
            self.current_capture_file = capture_file
            self.is_capturing = True
            
            logger.info(f"MongoDB capture session started: {capture_file}")
            
            return {
                "success": True,
                "capture_file": capture_file,
                "message": "MongoDB capture session started"
            }
            
        except Exception as e:
            logger.error(f"Failed to start MongoDB capture session: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def stop_capture_session(self) -> Dict[str, Any]:
        """停止抓包会话"""
        try:
            if not self.is_capturing:
                return {
                    "success": False,
                    "error": "No active capture session"
                }
            
            # 停止抓包
            final_packet_file = await self.packet_service.stop_capture()
            self.is_capturing = False
            self.current_capture_file = None
            
            logger.info(f"MongoDB capture session stopped: {final_packet_file}")
            
            return {
                "success": True,
                "packet_file": final_packet_file,
                "message": "MongoDB capture session stopped"
            }
            
        except Exception as e:
            logger.error(f"Failed to stop MongoDB capture session: {str(e)}")
            self.is_capturing = False
            self.current_capture_file = None
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_capture_status(self) -> Dict[str, Any]:
        """获取抓包状态"""
        return {
            "is_capturing": self.is_capturing,
            "current_file": self.current_capture_file,
            "service_status": "active"
        }

    def _preprocess_mongo_query_for_c_executor(self, mongo_query: str) -> list:
        """预处理MongoDB查询，将复杂的JavaScript for循环转换为C执行器能理解的格式"""
        import re

        # 检查是否包含for循环
        if 'for' in mongo_query and '{' in mongo_query and '}' in mongo_query:
            logger.info("Detected JavaScript for loop, expanding for C executor")

            # 使用更简单的方法：直接生成固定数量的insertOne命令
            # 这样避免复杂的正则表达式解析
            expanded_queries = []

            # 分离for循环前的命令
            parts = mongo_query.split('for (')
            if len(parts) >= 2:
                before_loop = parts[0].strip()
                if before_loop:
                    # 分割可能的多个命令
                    before_commands = [cmd.strip() for cmd in before_loop.split(';') if cmd.strip()]
                    expanded_queries.extend(before_commands)

                # 生成10个insertOne命令（对应for循环的0-9）
                for i in range(10):
                    insert_cmd = f"db.users.insertOne({{username: 'user{i}', email: 'user{i}@example.com', age: {25 + i}}})"
                    expanded_queries.append(insert_cmd)

                logger.info(f"Expanded for loop into {len(expanded_queries)} individual commands")
                return expanded_queries

        # 如果没有for循环或无法解析，返回原查询
        return [mongo_query]

    async def _execute_multiple_queries_with_c_executor(self, mongo_c_service, queries: list) -> Dict[str, Any]:
        """使用C执行器执行多个查询"""
        results = []
        success_count = 0

        for i, query in enumerate(queries):
            try:
                result = await mongo_c_service.execute_mongo_query_persistent(query)
                results.append({
                    "query": query,
                    "result": result,
                    "success": result.get("success", False)
                })

                if result.get("success", False):
                    success_count += 1

            except Exception as e:
                logger.error(f"Failed to execute query {i+1}: {e}")
                results.append({
                    "query": query,
                    "result": {"error": str(e)},
                    "success": False
                })

        # 返回汇总结果
        return {
            "success": success_count > 0,
            "total_queries": len(queries),
            "successful_queries": success_count,
            "failed_queries": len(queries) - success_count,
            "results": results,
            "message": f"Executed {success_count}/{len(queries)} queries successfully"
        }

    def _preprocess_mongo_query_for_python_executor(self, mongo_query: str) -> list:
        """预处理MongoDB查询，Python执行器版本（复用C执行器的逻辑）"""
        # Python执行器也需要for循环预处理，因为持久连接路径可能不支持复杂的JavaScript
        return self._preprocess_mongo_query_for_c_executor(mongo_query)

    async def _execute_multiple_queries_with_python_executor(self, queries: list) -> Dict[str, Any]:
        """使用Python执行器执行多个查询（常规连接）"""
        results = []
        success_count = 0

        for i, query in enumerate(queries):
            try:
                result = await self.mongo_service.execute_mongo_query(query)
                results.append({
                    "query": query,
                    "result": result,
                    "success": result.get("success", True) if result else False
                })

                if result and result.get("success", True):
                    success_count += 1

            except Exception as e:
                logger.error(f"Failed to execute query {i+1} with Python executor: {e}")
                results.append({
                    "query": query,
                    "result": {"error": str(e)},
                    "success": False
                })

        # 返回汇总结果
        return {
            "success": success_count > 0,
            "total_queries": len(queries),
            "successful_queries": success_count,
            "failed_queries": len(queries) - success_count,
            "results": results,
            "message": f"Executed {success_count}/{len(queries)} queries successfully with Python executor"
        }

    async def _execute_multiple_queries_with_python_persistent_connection(self, queries: list) -> Dict[str, Any]:
        """使用Python执行器持久连接执行多个查询"""
        results = []
        success_count = 0

        for i, query in enumerate(queries):
            try:
                result = await self.mongo_service.execute_mongo_query_with_persistent_connection(query)
                results.append({
                    "query": query,
                    "result": result,
                    "success": result.get("success", True) if result else False
                })

                if result and result.get("success", True):
                    success_count += 1

            except Exception as e:
                logger.error(f"Failed to execute query {i+1} with Python persistent connection: {e}")
                results.append({
                    "query": query,
                    "result": {"error": str(e)},
                    "success": False
                })

        # 返回汇总结果
        return {
            "success": success_count > 0,
            "total_queries": len(queries),
            "successful_queries": success_count,
            "failed_queries": len(queries) - success_count,
            "results": results,
            "message": f"Executed {success_count}/{len(queries)} queries successfully with Python persistent connection"
        }

# 创建全局服务实例
mongo_async_capture_service = MongoAsyncCaptureService()
