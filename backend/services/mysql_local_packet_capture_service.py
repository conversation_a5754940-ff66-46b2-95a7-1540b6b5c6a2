"""
MySQL数据包捕获服务 - 本地抓包
"""

import asyncio
import logging
import traceback
import os
from typing import Optional, Dict, Any
from datetime import datetime

from services.local_tcpdump_service import LocalTcpdumpService
from utils.local_network_utils import LocalNetworkUtils
from services.capture_file_service import capture_file_service
from models.capture_file import CaptureFileCreate

logger = logging.getLogger(__name__)

class MySQLLocalPacketCaptureService:
    """MySQL数据包捕获服务 - 本地抓包"""

    def __init__(self):
        self.is_capturing = False
        self.current_file: Optional[str] = None
        self.mysql_port = 3306  # MySQL默认端口
        
        # 使用绝对路径确保抓包目录正确
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = os.path.dirname(current_dir)
        self.capture_dir = os.path.join(backend_dir, "captures")
        
        logger.info(f"MySQL capture service initialized with capture_dir: {self.capture_dir}")

        # 确保本地captures目录存在
        os.makedirs(self.capture_dir, exist_ok=True)

        # 本地tcpdump服务
        self.local_tcpdump_service = LocalTcpdumpService(self.capture_dir)

    async def start_local_capture(self, mysql_host: str = "**************", mysql_port: int = 3306) -> str:
        """
        启动本地MySQL数据包捕获
        直接监控指定主机和端口的流量

        Args:
            mysql_host: MySQL主机地址
            mysql_port: MySQL端口

        Returns:
            str: 抓包文件路径
        """
        logger.info(f"Starting MySQL packet capture - Target server: {mysql_host}:{mysql_port}")

        try:
            # 保存当前抓包配置，用于后续保存到数据库
            self._current_target_host = mysql_host
            self._current_target_port = mysql_port

            # 获取最佳本地网络接口
            interface = LocalNetworkUtils.get_best_local_interface()
            logger.info(f"Selected local interface: {interface}")

            # 构建过滤表达式 - 监控指定主机和端口的流量
            filter_expr = f"host {mysql_host} and tcp port {mysql_port}"
            logger.info(f"Monitoring traffic: {filter_expr}")

            # 启动本地tcpdump
            capture_file = await self.local_tcpdump_service.start_capture(
                database_type="mysql",
                target_port=mysql_port,
                interface=interface,
                filter_expression=filter_expr
            )

            self.current_file = capture_file
            self.is_capturing = True

            logger.info(f"MySQL packet capture started successfully: {capture_file}")
            logger.info(f"Monitoring traffic to/from {mysql_host}:{mysql_port}")
            return capture_file

        except Exception as e:
            logger.error(f"Failed to start MySQL local capture: {str(e)}")
            raise Exception(f"MySQL本地抓包启动失败: {str(e)}")

    async def start_capture(self, target_host: str = None, target_port: int = None, server_config_id: int = None) -> str:
        """启动MySQL数据包捕获"""
        mysql_host = target_host or "**************"  # 默认使用远程MySQL服务器
        mysql_port = target_port or 3306

        logger.info(f"MySQL抓包配置 - Host: {mysql_host}, Port: {mysql_port}")

        # 如果检测到抓包状态异常，强制重置
        if self.is_capturing:
            logger.warning("检测到MySQL抓包状态异常，强制重置状态")
            await self.stop_capture()

        # 启动抓包
        try:
            logger.info(f"Starting packet capture to monitor MySQL server: {mysql_host}:{mysql_port}")
            return await self.start_local_capture(mysql_host, mysql_port)
        except Exception as e:
            logger.error(f"MySQL capture failed: {str(e)}")
            raise Exception(f"MySQL抓包启动失败: {str(e)}")

    async def stop_capture(self) -> Optional[str]:
        """停止MySQL数据包捕获"""
        logger.info("Stopping MySQL packet capture")

        try:
            if not self.is_capturing:
                logger.warning("MySQL packet capture is not running")
                return None

            # 停止本地tcpdump
            captured_file = await self.local_tcpdump_service.stop_capture()

            self.is_capturing = False
            self.current_file = None

            if captured_file and os.path.exists(captured_file):
                file_size = os.path.getsize(captured_file)
                logger.info(f"MySQL packet capture completed: {captured_file} ({file_size} bytes)")

                # 如果文件太小，删除空文件
                if file_size <= 24:  # pcap文件头大小，表示没有实际数据包
                    logger.warning(f"MySQL capture file is empty (only {file_size} bytes), deleting it")
                    try:
                        os.remove(captured_file)
                        logger.info(f"Deleted empty capture file: {captured_file}")
                    except Exception as e:
                        logger.error(f"Failed to delete empty capture file: {e}")
                    return None
                elif file_size < 100:
                    logger.warning(f"MySQL capture file is very small ({file_size} bytes), may contain no actual packets")

                # 保存文件信息到数据库
                await self._save_capture_file_to_db(captured_file)

                # 返回文件名（由path_manager处理绝对路径转换）
                filename = os.path.basename(captured_file)
                logger.info(f"Returning capture filename: {filename} (full path: {captured_file})")
                return filename
            else:
                logger.warning("MySQL capture file not found")
                return None

        except Exception as e:
            logger.error(f"Error stopping MySQL packet capture: {str(e)}")
            return None

    def get_capture_status(self) -> Dict[str, Any]:
        """获取MySQL抓包状态"""
        status = {
            'is_capturing': self.is_capturing,
            'current_file': self.current_file,
            'database_type': 'mysql',
            'capture_method': 'local'
        }
        
        # 如果有本地tcpdump服务，获取其状态
        if hasattr(self, 'local_tcpdump_service'):
            tcpdump_status = self.local_tcpdump_service.get_capture_status()
            status.update(tcpdump_status)
        
        return status

    async def force_reset_state(self):
        """强制重置抓包状态"""
        logger.warning("Force resetting MySQL capture state")
        
        try:
            # 停止本地tcpdump
            if hasattr(self, 'local_tcpdump_service'):
                await self.local_tcpdump_service.stop_capture()
        except Exception as e:
            logger.error(f"Error during force reset: {str(e)}")
        finally:
            self.is_capturing = False
            self.current_file = None
            logger.info("MySQL capture state reset completed")

    async def cleanup(self):
        """清理资源"""
        try:
            if self.is_capturing:
                await self.stop_capture()
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")

    async def _save_capture_file_to_db(self, capture_file_path: str):
        """保存抓包文件信息到数据库"""
        try:
            if not os.path.exists(capture_file_path):
                logger.warning(f"Capture file does not exist: {capture_file_path}")
                return

            file_size = os.path.getsize(capture_file_path)
            filename = os.path.basename(capture_file_path)

            # 从当前抓包配置中获取目标主机和端口信息
            target_host = getattr(self, '_current_target_host', '**************')
            target_port = getattr(self, '_current_target_port', 3306)

            capture_data = CaptureFileCreate(
                filename=filename,
                file_path=capture_file_path,
                file_size=file_size,
                database_type="mysql",
                target_host=target_host,
                target_port=target_port,
                description=f"MySQL packet capture from {target_host}:{target_port}"
            )

            file_id = await capture_file_service.save_capture_file(capture_data)
            logger.info(f"Saved MySQL capture file to database with ID: {file_id}")

        except Exception as e:
            logger.error(f"Failed to save capture file to database: {e}")
            # 不抛出异常，避免影响抓包流程

    def __del__(self):
        """析构函数"""
        try:
            if self.is_capturing:
                # 注意：在析构函数中不能使用async/await
                logger.warning("MySQL capture service destroyed while capturing")
        except Exception as e:

            logger.error(f"操作失败: {type(e).__name__}: {str(e)}")

            logger.error(f"异常堆栈跟踪: {traceback.format_exc()}")
