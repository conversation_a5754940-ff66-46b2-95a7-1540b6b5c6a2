"""
Redis配置
"""

import os
from typing import Optional
try:
    from arq import create_pool
    from arq.connections import RedisSettings
    ARQ_AVAILABLE = True
except ImportError:
    ARQ_AVAILABLE = False
    create_pool = None
    RedisSettings = None
import redis.asyncio as aioredis
import logging

logger = logging.getLogger(__name__)

class RedisConfig:
    """Redis配置类"""
    
    def __init__(self):
        self.host = os.getenv('REDIS_HOST', '************')
        self.port = int(os.getenv('REDIS_PORT', 6379))
        self.db = int(os.getenv('REDIS_DB', 0))
        self.password = os.getenv('REDIS_PASSWORD', None)
        self.max_connections = int(os.getenv('REDIS_MAX_CONNECTIONS', 20))
        
    @property
    def redis_settings(self):
        """获取Arq Redis设置"""
        if ARQ_AVAILABLE and RedisSettings:
            return RedisSettings(
                host=self.host,
                port=self.port,
                database=self.db,
                password=self.password
            )
        return None
    
    @property
    def redis_url(self) -> str:
        """获取Redis URL"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        return f"redis://{self.host}:{self.port}/{self.db}"

# 全局Redis配置实例
redis_config = RedisConfig()

class RedisManager:
    """Redis连接管理器"""

    def __init__(self):
        self._pool: Optional[aioredis.ConnectionPool] = None
        self._redis: Optional[aioredis.Redis] = None
        self._arq_pool = None
        self._initialized = False

    async def initialize(self):
        """初始化Redis连接"""
        try:
            # 如果已经初始化且连接健康，直接返回
            if self._initialized and await self._check_health():
                return

            # 清理旧连接
            await self._cleanup_old_connections()

            # 创建aioredis连接
            self._redis = aioredis.from_url(
                redis_config.redis_url,
                max_connections=redis_config.max_connections
            )

            # 创建Arq连接池（如果可用）
            if ARQ_AVAILABLE and create_pool:
                self._arq_pool = await create_pool(redis_config.redis_settings)
            else:
                logger.warning("ARQ不可用，跳过ARQ连接池创建")

            # 测试连接
            await self._redis.ping()
            self._initialized = True
            logger.info(f"Redis连接成功: {redis_config.host}:{redis_config.port}")

        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self._initialized = False
            raise

    async def _check_health(self) -> bool:
        """检查连接健康状态"""
        try:
            if not self._redis:
                logger.debug("Redis客户端不存在")
                return False

            # 检查Redis连接
            await self._redis.ping()
            logger.debug("Redis连接健康")

            # 检查ARQ连接池
            if self._arq_pool:
                # 尝试ping测试（这是最可靠的健康检查方式）
                try:
                    await self._arq_pool.ping()
                    logger.debug("ARQ连接池ping成功")
                except Exception as ping_error:
                    logger.warning(f"ARQ连接池ping失败: {ping_error}")
                    return False

                # 额外检查连接池状态（可选）
                if hasattr(self._arq_pool, 'connection_pool'):
                    # ARQ 0.25.0+ 使用 connection_pool 属性
                    logger.debug("ARQ连接池使用connection_pool")
                elif hasattr(self._arq_pool, '_pool'):
                    # 兼容旧版本
                    if self._arq_pool._pool.closed:
                        logger.warning("ARQ连接池已关闭")
                        return False
                    logger.debug("ARQ连接池使用_pool")

                logger.debug("ARQ连接池健康")
            else:
                logger.debug("ARQ连接池不存在")
                return False

            return True
        except Exception as e:
            logger.warning(f"连接健康检查失败: {e}")
            return False

    async def _cleanup_old_connections(self):
        """清理旧连接"""
        try:
            if self._arq_pool:
                try:
                    # ARQ连接池的正确关闭方式
                    if hasattr(self._arq_pool, 'close'):
                        await self._arq_pool.close()
                    elif hasattr(self._arq_pool, '_pool'):
                        # 直接关闭底层连接池
                        self._arq_pool._pool.close()
                        await self._arq_pool._pool.wait_closed()
                except Exception as e:
                    logger.warning(f"清理ARQ连接池时出错: {e}")
                finally:
                    self._arq_pool = None

            if self._redis:
                try:
                    await self._redis.close()
                except Exception as e:
                    logger.warning(f"清理Redis连接时出错: {e}")
                finally:
                    self._redis = None

        except Exception as e:
            logger.warning(f"清理旧连接时出错: {e}")
    
    async def close(self):
        """关闭Redis连接"""
        try:
            await self._cleanup_old_connections()
            self._initialized = False
            logger.info("Redis连接已关闭")
        except Exception as e:
            logger.error(f"关闭Redis连接时出错: {e}")

    @property
    def redis(self) -> aioredis.Redis:
        """获取Redis客户端"""
        if not self._redis:
            raise RuntimeError("Redis未初始化，请先调用initialize()")
        return self._redis

    @property
    def arq_pool(self):
        """获取Arq连接池"""
        if not self._arq_pool:
            raise RuntimeError("Arq连接池未初始化，请先调用initialize()")
        return self._arq_pool

    async def ensure_connection(self):
        """确保连接可用，如果不可用则重新初始化"""
        if not self._initialized or not await self._check_health():
            logger.info("Redis连接不健康，重新初始化...")
            await self.initialize()

# 全局Redis管理器实例
redis_manager = RedisManager()
