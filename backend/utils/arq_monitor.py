#!/usr/bin/env python3
"""
ARQ监控日志系统
提供完整的ARQ任务监控、日志分析和调试功能
"""

import asyncio
import logging
import json
import time
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import signal
import threading

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

from config.redis_config import redis_manager
from utils.config import Config

@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    function_name: str
    status: str  # queued, in_progress, complete, failed
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration: Optional[float] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    retry_count: int = 0
    worker_id: Optional[str] = None

@dataclass
class WorkerInfo:
    """Worker信息"""
    worker_id: str
    pid: int
    started_at: datetime
    last_heartbeat: datetime
    current_jobs: int
    total_completed: int
    total_failed: int
    status: str  # active, inactive, dead

class ARQMonitor:
    """ARQ监控器"""
    
    def __init__(self, log_level: str = "INFO"):
        self.log_level = log_level
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 监控数据
        self.tasks: Dict[str, TaskInfo] = {}
        self.workers: Dict[str, WorkerInfo] = {}
        self.task_history = deque(maxlen=1000)  # 保留最近1000个任务
        self.performance_metrics = defaultdict(list)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        self.stats_update_interval = 5  # 秒
        
        # 日志文件路径
        self.log_dir = Path("logs/arq_monitor")
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建专门的ARQ监控日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.monitor_log_file = self.log_dir / f"arq_monitor_{timestamp}.log"
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def setup_logging(self):
        """设置日志配置"""
        # 创建ARQ监控专用的日志记录器
        monitor_logger = logging.getLogger('arq_monitor')
        monitor_logger.setLevel(getattr(logging, self.log_level.upper()))
        
        # 清除现有处理器
        for handler in monitor_logger.handlers[:]:
            monitor_logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, self.log_level.upper()))
        console_formatter = logging.Formatter(
            '%(asctime)s - ARQ监控 - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        monitor_logger.addHandler(console_handler)
        
        # 文件处理器
        log_dir = Path("logs/arq_monitor")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(
            log_dir / f"arq_monitor_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        monitor_logger.addHandler(file_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，正在停止监控...")
        self.stop_monitoring()
        sys.exit(0)
    
    async def initialize(self):
        """初始化监控器"""
        try:
            await redis_manager.initialize()
            self.logger.info("ARQ监控器初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"ARQ监控器初始化失败: {e}")
            return False
    
    async def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.logger.warning("监控已在运行中")
            return
        
        self.is_monitoring = True
        self.logger.info("开始ARQ监控...")
        
        # 启动监控循环
        try:
            while self.is_monitoring:
                await self._collect_metrics()
                await self._update_task_status()
                await self._update_worker_status()
                await self._log_statistics()
                await asyncio.sleep(self.stats_update_interval)
        except Exception as e:
            self.logger.error(f"监控循环出错: {e}")
        finally:
            self.is_monitoring = False
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.logger.info("ARQ监控已停止")
    
    async def _collect_metrics(self):
        """收集监控指标"""
        try:
            # 获取Redis连接信息
            redis_info = await redis_manager.redis.info()
            
            # 记录Redis状态
            self.performance_metrics['redis_memory'].append({
                'timestamp': datetime.now(),
                'used_memory': redis_info.get('used_memory', 0),
                'connected_clients': redis_info.get('connected_clients', 0)
            })
            
            # 获取队列长度
            queue_length = await redis_manager.redis.llen('arq:capture_tasks')
            self.performance_metrics['queue_length'].append({
                'timestamp': datetime.now(),
                'length': queue_length
            })
            
            # 清理旧数据（保留最近1小时）
            cutoff_time = datetime.now() - timedelta(hours=1)
            for metric_name in self.performance_metrics:
                self.performance_metrics[metric_name] = [
                    item for item in self.performance_metrics[metric_name]
                    if item['timestamp'] > cutoff_time
                ]
                
        except Exception as e:
            self.logger.error(f"收集监控指标失败: {e}")
    
    async def _update_task_status(self):
        """更新任务状态"""
        try:
            # 扫描所有ARQ相关的键
            patterns = [
                'arq:job:*',
                'arq:result:*',
                'arq:in-progress:*'
            ]
            
            for pattern in patterns:
                async for key in redis_manager.redis.scan_iter(match=pattern):
                    await self._process_task_key(key)
                    
        except Exception as e:
            self.logger.error(f"更新任务状态失败: {e}")
    
    async def _process_task_key(self, key: str):
        """处理任务键"""
        try:
            if isinstance(key, bytes):
                key = key.decode('utf-8')
            
            if key.startswith('arq:job:'):
                task_id = key.replace('arq:job:', '')
                job_data = await redis_manager.redis.get(key)
                if job_data:
                    job_info = json.loads(job_data)
                    await self._update_task_info(task_id, job_info, 'queued')
            
            elif key.startswith('arq:in-progress:'):
                task_id = key.replace('arq:in-progress:', '')
                await self._update_task_info(task_id, {}, 'in_progress')
            
            elif key.startswith('arq:result:'):
                task_id = key.replace('arq:result:', '')
                result_data = await redis_manager.redis.get(key)
                if result_data:
                    result_info = json.loads(result_data)
                    status = 'complete' if result_info.get('success') else 'failed'
                    await self._update_task_info(task_id, result_info, status)
                    
        except Exception as e:
            self.logger.debug(f"处理任务键 {key} 失败: {e}")
    
    async def _update_task_info(self, task_id: str, data: dict, status: str):
        """更新任务信息"""
        try:
            if task_id not in self.tasks:
                self.tasks[task_id] = TaskInfo(
                    task_id=task_id,
                    function_name=data.get('function', 'unknown'),
                    status=status,
                    created_at=datetime.now()
                )
            
            task = self.tasks[task_id]
            old_status = task.status
            task.status = status
            
            # 更新时间戳
            if status == 'in_progress' and old_status == 'queued':
                task.started_at = datetime.now()
            elif status in ['complete', 'failed'] and task.started_at:
                task.completed_at = datetime.now()
                task.duration = (task.completed_at - task.started_at).total_seconds()
            
            # 更新结果和错误信息
            if 'result' in data:
                task.result = data['result']
            if 'error' in data:
                task.error = data['error']
            
            # 记录状态变化
            if old_status != status:
                self.logger.info(f"任务 {task_id[:8]}... 状态变化: {old_status} -> {status}")
                
                # 添加到历史记录
                self.task_history.append({
                    'task_id': task_id,
                    'function_name': task.function_name,
                    'status_change': f"{old_status} -> {status}",
                    'timestamp': datetime.now()
                })
                
        except Exception as e:
            self.logger.error(f"更新任务信息失败: {e}")
    
    async def _update_worker_status(self):
        """更新Worker状态"""
        try:
            # 扫描活跃的worker
            async for key in redis_manager.redis.scan_iter(match='worker:active:*'):
                if isinstance(key, bytes):
                    key = key.decode('utf-8')
                
                worker_id = key.replace('worker:active:', '')
                
                # 检查worker是否还活跃
                ttl = await redis_manager.redis.ttl(key)
                if ttl > 0:
                    if worker_id not in self.workers:
                        self.workers[worker_id] = WorkerInfo(
                            worker_id=worker_id,
                            pid=int(worker_id.split('_')[1]) if '_' in worker_id else 0,
                            started_at=datetime.now(),
                            last_heartbeat=datetime.now(),
                            current_jobs=0,
                            total_completed=0,
                            total_failed=0,
                            status='active'
                        )
                    else:
                        self.workers[worker_id].last_heartbeat = datetime.now()
                        self.workers[worker_id].status = 'active'
                else:
                    # Worker已过期
                    if worker_id in self.workers:
                        self.workers[worker_id].status = 'inactive'
                        
        except Exception as e:
            self.logger.error(f"更新Worker状态失败: {e}")
    
    async def _log_statistics(self):
        """记录统计信息"""
        try:
            # 计算任务统计
            task_stats = defaultdict(int)
            for task in self.tasks.values():
                task_stats[task.status] += 1
            
            # 计算Worker统计
            worker_stats = defaultdict(int)
            for worker in self.workers.values():
                worker_stats[worker.status] += 1
            
            # 获取队列长度
            queue_length = await redis_manager.redis.llen('arq:capture_tasks')
            
            # 记录统计信息
            stats_msg = (
                f"ARQ状态统计 - "
                f"队列: {queue_length}, "
                f"任务: {dict(task_stats)}, "
                f"Worker: {dict(worker_stats)}"
            )
            self.logger.info(stats_msg)
            
            # 写入详细统计到文件
            await self._write_detailed_stats(task_stats, worker_stats, queue_length)
            
        except Exception as e:
            self.logger.error(f"记录统计信息失败: {e}")
    
    async def _write_detailed_stats(self, task_stats: dict, worker_stats: dict, queue_length: int):
        """写入详细统计信息到文件"""
        try:
            stats_data = {
                'timestamp': datetime.now().isoformat(),
                'queue_length': queue_length,
                'task_stats': dict(task_stats),
                'worker_stats': dict(worker_stats),
                'total_tasks': len(self.tasks),
                'recent_tasks': len(self.task_history),
                'performance_metrics': {
                    name: len(metrics) for name, metrics in self.performance_metrics.items()
                }
            }
            
            # 写入JSON格式的统计文件
            stats_file = self.log_dir / f"stats_{datetime.now().strftime('%Y%m%d')}.jsonl"
            with open(stats_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(stats_data, ensure_ascii=False) + '\n')
                
        except Exception as e:
            self.logger.error(f"写入详细统计失败: {e}")

    def get_task_summary(self) -> Dict[str, Any]:
        """获取任务摘要"""
        summary = {
            'total_tasks': len(self.tasks),
            'status_breakdown': defaultdict(int),
            'function_breakdown': defaultdict(int),
            'average_duration': 0,
            'recent_failures': []
        }

        durations = []
        for task in self.tasks.values():
            summary['status_breakdown'][task.status] += 1
            summary['function_breakdown'][task.function_name] += 1

            if task.duration:
                durations.append(task.duration)

            if task.status == 'failed' and task.error:
                summary['recent_failures'].append({
                    'task_id': task.task_id[:8],
                    'function': task.function_name,
                    'error': task.error,
                    'timestamp': task.completed_at.isoformat() if task.completed_at else None
                })

        if durations:
            summary['average_duration'] = sum(durations) / len(durations)

        return summary

    def get_worker_summary(self) -> Dict[str, Any]:
        """获取Worker摘要"""
        summary = {
            'total_workers': len(self.workers),
            'active_workers': sum(1 for w in self.workers.values() if w.status == 'active'),
            'inactive_workers': sum(1 for w in self.workers.values() if w.status == 'inactive'),
            'worker_details': []
        }

        for worker in self.workers.values():
            summary['worker_details'].append({
                'worker_id': worker.worker_id,
                'pid': worker.pid,
                'status': worker.status,
                'uptime': (datetime.now() - worker.started_at).total_seconds() if worker.started_at else 0,
                'last_heartbeat': worker.last_heartbeat.isoformat() if worker.last_heartbeat else None
            })

        return summary

    async def get_queue_info(self) -> Dict[str, Any]:
        """获取队列信息"""
        try:
            queue_length = await redis_manager.redis.llen('arq:capture_tasks')

            # 获取队列中的任务预览
            queue_preview = []
            tasks = await redis_manager.redis.lrange('arq:capture_tasks', 0, 4)  # 获取前5个任务

            for task_data in tasks:
                try:
                    task_info = json.loads(task_data)
                    queue_preview.append({
                        'function': task_info.get('function', 'unknown'),
                        'args': str(task_info.get('args', []))[:100] + '...' if len(str(task_info.get('args', []))) > 100 else str(task_info.get('args', [])),
                        'enqueue_time': task_info.get('enqueue_time')
                    })
                except:
                    queue_preview.append({'function': 'parse_error', 'args': '', 'enqueue_time': None})

            return {
                'queue_length': queue_length,
                'queue_preview': queue_preview
            }
        except Exception as e:
            self.logger.error(f"获取队列信息失败: {e}")
            return {'queue_length': 0, 'queue_preview': []}

    def print_dashboard(self):
        """打印监控面板"""
        os.system('clear' if os.name == 'posix' else 'cls')

        print("=" * 80)
        print(f"🔍 ARQ监控面板 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 任务摘要
        task_summary = self.get_task_summary()
        print("\n📊 任务统计:")
        print(f"  总任务数: {task_summary['total_tasks']}")
        for status, count in task_summary['status_breakdown'].items():
            print(f"  {status}: {count}")
        if task_summary['average_duration'] > 0:
            print(f"  平均执行时间: {task_summary['average_duration']:.2f}秒")

        # Worker摘要
        worker_summary = self.get_worker_summary()
        print(f"\n👷 Worker统计:")
        print(f"  总Worker数: {worker_summary['total_workers']}")
        print(f"  活跃Worker: {worker_summary['active_workers']}")
        print(f"  非活跃Worker: {worker_summary['inactive_workers']}")

        # 最近失败的任务
        if task_summary['recent_failures']:
            print(f"\n❌ 最近失败的任务 (最多显示5个):")
            for failure in task_summary['recent_failures'][:5]:
                print(f"  {failure['task_id']} - {failure['function']} - {failure['error'][:50]}...")

        # 性能指标
        if self.performance_metrics:
            print(f"\n📈 性能指标:")
            for metric_name, metrics in self.performance_metrics.items():
                if metrics:
                    latest = metrics[-1]
                    if metric_name == 'redis_memory':
                        print(f"  Redis内存: {latest.get('used_memory', 0) / 1024 / 1024:.1f}MB")
                        print(f"  Redis连接数: {latest.get('connected_clients', 0)}")
                    elif metric_name == 'queue_length':
                        print(f"  队列长度: {latest.get('length', 0)}")

        print("\n" + "=" * 80)
        print("按 Ctrl+C 退出监控")
        print("=" * 80)

async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='ARQ监控工具')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    parser.add_argument('--interval', type=int, default=5, help='监控更新间隔（秒）')
    parser.add_argument('--dashboard', action='store_true', help='显示实时监控面板')
    parser.add_argument('--export', help='导出监控数据到文件')

    args = parser.parse_args()

    # 创建监控器
    monitor = ARQMonitor(log_level=args.log_level)
    monitor.stats_update_interval = args.interval

    # 初始化
    if not await monitor.initialize():
        print("❌ 监控器初始化失败")
        return 1

    print("✅ ARQ监控器启动成功")
    print(f"📝 日志文件: {monitor.monitor_log_file}")
    print(f"📊 统计文件: {monitor.log_dir}")

    try:
        if args.dashboard:
            # 启动面板模式
            dashboard_task = asyncio.create_task(monitor.start_monitoring())

            # 定期更新面板显示
            while monitor.is_monitoring:
                monitor.print_dashboard()
                await asyncio.sleep(args.interval)

        else:
            # 启动普通监控模式
            await monitor.start_monitoring()

    except KeyboardInterrupt:
        print("\n⏹️ 监控被用户中断")
    except Exception as e:
        print(f"❌ 监控出错: {e}")
        return 1
    finally:
        monitor.stop_monitoring()

    return 0

if __name__ == '__main__':
    asyncio.run(main())
