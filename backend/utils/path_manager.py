"""
统一路径管理器 - 管理所有项目路径
"""

import os
from typing import Optional

class PathManager:
    """统一路径管理器"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(PathManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_paths()
            self._initialized = True
    
    def _setup_paths(self):
        """设置所有路径"""
        # 获取项目根目录（ai_sql_pcap）
        current_file = os.path.abspath(__file__)
        self.backend_root = os.path.dirname(os.path.dirname(current_file))  # backend目录
        self.project_root = os.path.dirname(self.backend_root)  # 项目根目录
        
        # 抓包文件目录 - 统一使用backend/captures
        self.captures_dir = os.path.join(self.backend_root, "captures")
        
        # 确保目录存在
        os.makedirs(self.captures_dir, exist_ok=True)
    
    def get_captures_dir(self) -> str:
        """获取抓包文件目录"""
        return self.captures_dir
    
    def get_capture_file_path(self, filename: str) -> str:
        """获取抓包文件的完整路径"""
        return os.path.join(self.captures_dir, filename)
    
    def get_backend_root(self) -> str:
        """获取backend根目录"""
        return self.backend_root
    
    def get_project_root(self) -> str:
        """获取项目根目录"""
        return self.project_root
    
    def resolve_capture_file_path(self, file_path: str) -> str:
        """
        解析抓包文件路径，返回绝对路径用于文件操作
        """
        if os.path.isabs(file_path):
            return file_path

        # 如果是相对路径，统一使用backend/captures目录
        # 处理 "captures/filename" 或 "filename" 格式
        if file_path.startswith("captures/"):
            filename = file_path[9:]  # 去掉 "captures/" 前缀
        else:
            filename = os.path.basename(file_path)

        return os.path.join(self.captures_dir, filename)

    def get_relative_capture_path(self, filename: str) -> str:
        """
        获取相对于backend目录的抓包文件路径
        用于数据库存储，格式：captures/filename
        """
        # 确保只返回文件名，不包含路径
        filename = os.path.basename(filename)
        return os.path.join("captures", filename)


# 创建全局单例实例
path_manager = PathManager()
