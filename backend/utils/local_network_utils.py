"""
本地网络工具类
用于检测本地网络接口和未使用的端口
"""

import socket
import subprocess
import psutil
import logging
from typing import List, Optional, Dict, Any
import re
import os
import sys

# 添加config目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'config'))

try:
    from local_capture_config import LocalCaptureConfig
except ImportError:
    # 如果配置文件不存在，使用默认配置
    class LocalCaptureConfig:
        INTERFACE_PRIORITY = ['eth0', 'ens3', 'ens33', 'enp0s3', 'wlan0', 'wifi0', 'docker0', 'lo']
        DEFAULT_PORT_RANGE = {'start': 8000, 'end': 9000}

logger = logging.getLogger(__name__)

class LocalNetworkUtils:
    """本地网络工具类"""
    
    @staticmethod
    def get_local_network_interfaces() -> List[Dict[str, Any]]:
        """
        获取本地网络接口信息
        
        Returns:
            List[Dict]: 网络接口信息列表
        """
        interfaces = []
        
        try:
            # 使用psutil获取网络接口信息
            net_if_addrs = psutil.net_if_addrs()
            net_if_stats = psutil.net_if_stats()
            
            for interface_name, addresses in net_if_addrs.items():
                # 跳过回环接口（除非没有其他接口）
                if interface_name == 'lo' or interface_name.startswith('lo'):
                    continue
                    
                # 获取接口状态
                stats = net_if_stats.get(interface_name)
                if not stats or not stats.isup:
                    continue
                
                # 获取IP地址
                ipv4_addresses = []
                ipv6_addresses = []
                
                for addr in addresses:
                    if addr.family == socket.AF_INET:
                        ipv4_addresses.append(addr.address)
                    elif addr.family == socket.AF_INET6:
                        ipv6_addresses.append(addr.address)
                
                # 只保留有IP地址的接口
                if ipv4_addresses or ipv6_addresses:
                    interface_info = {
                        'name': interface_name,
                        'ipv4_addresses': ipv4_addresses,
                        'ipv6_addresses': ipv6_addresses,
                        'is_up': stats.isup,
                        'mtu': stats.mtu,
                        'speed': stats.speed if hasattr(stats, 'speed') else None
                    }
                    interfaces.append(interface_info)
                    
            # 如果没有找到其他接口，添加回环接口
            if not interfaces and 'lo' in net_if_addrs:
                lo_stats = net_if_stats.get('lo')
                if lo_stats and lo_stats.isup:
                    lo_addresses = net_if_addrs['lo']
                    ipv4_addresses = [addr.address for addr in lo_addresses if addr.family == socket.AF_INET]
                    ipv6_addresses = [addr.address for addr in lo_addresses if addr.family == socket.AF_INET6]
                    
                    interfaces.append({
                        'name': 'lo',
                        'ipv4_addresses': ipv4_addresses,
                        'ipv6_addresses': ipv6_addresses,
                        'is_up': lo_stats.isup,
                        'mtu': lo_stats.mtu,
                        'speed': None
                    })
                    
            logger.info(f"Found {len(interfaces)} local network interfaces")
            return interfaces
            
        except Exception as e:
            logger.error(f"Failed to get local network interfaces: {str(e)}")
            return []
    
    @staticmethod
    def get_best_local_interface() -> Optional[str]:
        """
        获取最佳的本地网络接口
        
        Returns:
            str: 最佳网络接口名称
        """
        # 1) 环境变量优先（遵循固定网口，不使用any）
        try:
            env_iface = os.environ.get('CAPTURE_INTERFACE')
            if env_iface:
                if LocalNetworkUtils.validate_interface_exists(env_iface):
                    logger.info(f"Selected interface from env CAPTURE_INTERFACE: {env_iface}")
                    return env_iface
                else:
                    logger.warning(f"CAPTURE_INTERFACE '{env_iface}' not found, falling back to auto selection")
        except Exception as e:
            logger.warning(f"Read CAPTURE_INTERFACE failed: {e}")

        # 优先使用有对端服务器网段的物理口：若存在以192.168.开头的地址，优先选择该地址所在接口
        interfaces = LocalNetworkUtils.get_local_network_interfaces()
        for iface in interfaces:
            if any(ip.startswith('192.168.') for ip in iface.get('ipv4_addresses', [])):
                logger.info(f"Selected interface by subnet heuristic: {iface['name']}")
                return iface['name']
        
        if not interfaces:
            logger.warning("No network interfaces found, using fallback 'lo'")
            return 'lo'
        
        # 优先级排序（使用配置）
        priority_names = LocalCaptureConfig.INTERFACE_PRIORITY[:-1]  # 排除lo接口
        
        # 首先尝试找到优先级高的接口
        for priority_name in priority_names:
            for interface in interfaces:
                if interface['name'] == priority_name:
                    logger.info(f"Selected priority interface: {priority_name}")
                    return priority_name
        
        # 如果没有找到优先级接口，选择第一个非回环接口
        for interface in interfaces:
            if interface['name'] != 'lo':
                logger.info(f"Selected first available interface: {interface['name']}")
                return interface['name']
        
        # 最后选择回环接口
        logger.info("Using loopback interface as fallback")
        return 'lo'
    
    @staticmethod
    def find_free_port(start_port: Optional[int] = None, end_port: Optional[int] = None) -> Optional[int]:
        """
        查找未使用的端口

        Args:
            start_port: 起始端口（如果不指定，使用配置默认值）
            end_port: 结束端口（如果不指定，使用配置默认值）

        Returns:
            int: 未使用的端口号，如果没有找到返回None
        """
        # 使用配置的默认端口范围
        if start_port is None or end_port is None:
            port_range = LocalCaptureConfig.DEFAULT_PORT_RANGE
            start_port = start_port or port_range['start']
            end_port = end_port or port_range['end']

        for port in range(start_port, end_port + 1):
            if LocalNetworkUtils.is_port_free(port):
                logger.info(f"Found free port: {port}")
                return port
        
        logger.warning(f"No free port found in range {start_port}-{end_port}")
        return None
    
    @staticmethod
    def is_port_free(port: int) -> bool:
        """
        检查端口是否空闲
        
        Args:
            port: 端口号
            
        Returns:
            bool: 端口是否空闲
        """
        try:
            # 检查TCP端口
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', port))
                if result == 0:
                    return False
            
            # 检查UDP端口
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock:
                sock.settimeout(1)
                try:
                    sock.bind(('127.0.0.1', port))
                    return True
                except OSError:
                    return False
                    
        except Exception as e:
            logger.warning(f"Error checking port {port}: {str(e)}")
            return False
    
    @staticmethod
    def get_listening_ports() -> List[int]:
        """
        获取当前正在监听的端口列表
        
        Returns:
            List[int]: 监听端口列表
        """
        listening_ports = []
        
        try:
            connections = psutil.net_connections(kind='inet')
            for conn in connections:
                if conn.status == psutil.CONN_LISTEN and conn.laddr:
                    listening_ports.append(conn.laddr.port)
            
            # 去重并排序
            listening_ports = sorted(list(set(listening_ports)))
            logger.info(f"Found {len(listening_ports)} listening ports")
            
        except Exception as e:
            logger.error(f"Failed to get listening ports: {str(e)}")
            
        return listening_ports
    
    @staticmethod
    def validate_interface_exists(interface_name: str) -> bool:
        """
        验证网络接口是否存在
        
        Args:
            interface_name: 网络接口名称
            
        Returns:
            bool: 接口是否存在
        """
        try:
            net_if_addrs = psutil.net_if_addrs()
            return interface_name in net_if_addrs
        except Exception as e:
            logger.error(f"Failed to validate interface {interface_name}: {str(e)}")
            return False
