import os
import logging
import logging.handlers
from datetime import datetime, timezone, timedelta
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 自动设置Oracle环境变量
def setup_oracle_environment():
    """自动设置Oracle客户端环境变量（跨平台兼容）"""
    import platform

    # 根据操作系统确定Oracle客户端路径
    system = platform.system().lower()

    if system == 'darwin':  # macOS
        oracle_lib_path = "/Users/<USER>/Desktop/qz_code/ai_sql_pcap/oracle_device/instantclient_19_8"
        lib_path_var = 'DYLD_LIBRARY_PATH'
    elif system == 'linux':  # Linux
        # Linux上的常见Oracle客户端路径
        possible_paths = [
            "/opt/oracle/instantclient_19_8",
            "/usr/lib/oracle/19.8/client64/lib",
            "/opt/oracle/instantclient",
            "/usr/local/oracle/instantclient_19_8",
            "./oracle_device/instantclient_19_8",  # 相对路径
            os.path.join(os.path.dirname(os.path.dirname(__file__)), "oracle_device", "instantclient_19_8")  # 项目相对路径
        ]
        oracle_lib_path = None
        for path in possible_paths:
            if os.path.exists(path):
                oracle_lib_path = path
                break

        if not oracle_lib_path:
            oracle_lib_path = "/opt/oracle/instantclient_19_8"  # 默认路径

        lib_path_var = 'LD_LIBRARY_PATH'
    else:  # Windows或其他系统
        oracle_lib_path = "./oracle_device/instantclient_19_8"
        lib_path_var = 'PATH'

    logger = logging.getLogger(__name__)
    logger.info(f"🔍 检测到操作系统: {system}")
    logger.info(f"🔍 Oracle客户端路径: {oracle_lib_path}")

    if os.path.exists(oracle_lib_path):
        # 设置库路径环境变量
        current_lib_path = os.environ.get(lib_path_var, '')
        if oracle_lib_path not in current_lib_path:
            new_lib_path = f"{oracle_lib_path}:{current_lib_path}" if current_lib_path else oracle_lib_path
            os.environ[lib_path_var] = new_lib_path
            os.putenv(lib_path_var, new_lib_path)

        # 设置ORACLE_HOME
        os.environ['ORACLE_HOME'] = oracle_lib_path
        os.putenv('ORACLE_HOME', oracle_lib_path)

        logger.info(f"✅ Oracle环境变量已自动设置:")
        logger.info(f"   ORACLE_HOME={oracle_lib_path}")
        logger.info(f"   {lib_path_var}={os.environ[lib_path_var]}")
        return True
    else:
        logger.warning(f"⚠️ Oracle Instant Client路径不存在: {oracle_lib_path}")
        logger.info(f"💡 Linux用户请安装Oracle Instant Client到以下路径之一:")
        if system == 'linux':
            for path in ["/opt/oracle/instantclient_19_8", "/usr/lib/oracle/19.8/client64/lib"]:
                logger.info(f"   - {path}")
        return False

# 自动设置Oracle环境变量
setup_oracle_environment()

class Config:
    """应用配置类"""

    # 时区配置 - 统一使用+8时区（Asia/Shanghai）
    TIMEZONE = timezone(timedelta(hours=8))
    TIMEZONE_NAME = 'Asia/Shanghai'

    # OpenAI配置
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    OPENAI_API_BASE = os.getenv('OPENAI_API_BASE', 'https://api.openai.com/v1')
    
    # MySQL配置
    MYSQL_HOST = os.getenv('MYSQL_HOST', '************')
    MYSQL_PORT = int(os.getenv('MYSQL_PORT', 3306))
    MYSQL_USER = os.getenv('MYSQL_USER', 'root')
    MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', '123456')
    MYSQL_DATABASE = os.getenv('MYSQL_DATABASE', 'ai_sql_pcap')

    # MongoDB配置
    MONGO_HOST = os.getenv('MONGO_HOST', '**************')
    MONGO_PORT = int(os.getenv('MONGO_PORT', 27017))
    MONGO_USER = os.getenv('MONGO_USER', 'admin')
    MONGO_PASSWORD = os.getenv('MONGO_PASSWORD', 'admin')
    MONGO_DATABASE = os.getenv('MONGO_DATABASE', 'ai-mongo-pcap')
    MONGO_AUTH_SOURCE = os.getenv('MONGO_AUTH_SOURCE', 'admin')

    # PostgreSQL配置
    POSTGRES_HOST = os.getenv('POSTGRES_HOST', '**************')
    POSTGRES_PORT = int(os.getenv('POSTGRES_PORT', 5432))
    POSTGRES_USER = os.getenv('POSTGRES_USER', 'postgres')
    POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD', 'postgres')
    POSTGRES_DATABASE = os.getenv('POSTGRES_DATABASE', 'postgres')

    # Oracle配置
    ORACLE_HOST = os.getenv('ORACLE_HOST', '*************')
    ORACLE_PORT = int(os.getenv('ORACLE_PORT', 1521))
    ORACLE_USER = os.getenv('ORACLE_USER', 'system')
    ORACLE_PASSWORD = os.getenv('ORACLE_PASSWORD', 'oracle')
    ORACLE_SERVICE_NAME = os.getenv('ORACLE_SERVICE_NAME', 'helowin')

    # DeepSeek API配置
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY', '***********************************')
    DEEPSEEK_BASE_URL = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
    
    # 应用配置
    APP_HOST = os.getenv('APP_HOST', '0.0.0.0')
    APP_PORT = int(os.getenv('APP_PORT', 8000))
    DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'
    
    # 抓包配置 - 使用统一路径管理器
    from utils.path_manager import path_manager
    CAPTURE_DIR = os.getenv('CAPTURE_DIR', path_manager.get_captures_dir())
    CAPTURE_INTERFACE = os.getenv('CAPTURE_INTERFACE', 'any')

    # 远程服务器配置
    REMOTE_HOST = os.getenv('REMOTE_HOST', '**************')
    REMOTE_USER = os.getenv('REMOTE_USER', 'root')
    REMOTE_PASSWORD = os.getenv('REMOTE_PASSWORD', 'QZ@1005#1005')
    REMOTE_CAPTURE_DIR = os.getenv('REMOTE_CAPTURE_DIR', '/tmp/mysql_captures')
    
    @classmethod
    def get_mysql_config(cls):
        """获取MySQL配置"""
        return {
            'host': cls.MYSQL_HOST,
            'port': cls.MYSQL_PORT,
            'user': cls.MYSQL_USER,
            'password': cls.MYSQL_PASSWORD,
            'database': cls.MYSQL_DATABASE
        }

    @classmethod
    def get_mongo_config(cls):
        """获取MongoDB配置"""
        return {
            'host': cls.MONGO_HOST,
            'port': cls.MONGO_PORT,
            'user': cls.MONGO_USER,
            'password': cls.MONGO_PASSWORD,
            'database': cls.MONGO_DATABASE,
            'auth_source': cls.MONGO_AUTH_SOURCE
        }

    @classmethod
    def get_postgres_config(cls):
        """获取PostgreSQL配置"""
        return {
            'host': cls.POSTGRES_HOST,
            'port': cls.POSTGRES_PORT,
            'user': cls.POSTGRES_USER,
            'password': cls.POSTGRES_PASSWORD,
            'database': cls.POSTGRES_DATABASE
        }

    @classmethod
    def setup_logging(cls):
        """设置统一的日志配置"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # 创建不同类型的日志目录
        (log_dir / "app").mkdir(exist_ok=True)
        (log_dir / "error").mkdir(exist_ok=True)
        (log_dir / "debug").mkdir(exist_ok=True)

        # 获取根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建格式化器
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s() - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )

        # 1. 控制台处理器 - 只显示INFO及以上级别
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        root_logger.addHandler(console_handler)

        # 2. 应用日志文件处理器 - 记录INFO及以上级别
        app_handler = logging.handlers.RotatingFileHandler(
            log_dir / "app" / "app.log",
            maxBytes=5*1024*1024,  # 5MB
            backupCount=10,
            encoding='utf-8'
        )
        app_handler.setLevel(logging.INFO)
        app_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(app_handler)

        # 3. 错误日志文件处理器 - 只记录ERROR及以上级别
        error_handler = logging.handlers.RotatingFileHandler(
            log_dir / "error" / "error.log",
            maxBytes=5*1024*1024,  # 5MB
            backupCount=20,  # 错误日志保留更多备份
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(error_handler)

        # 4. 调试日志文件处理器 - 记录所有级别
        debug_handler = logging.handlers.RotatingFileHandler(
            log_dir / "debug" / "debug.log",
            maxBytes=5*1024*1024,  # 5MB
            backupCount=5,
            encoding='utf-8'
        )
        debug_handler.setLevel(logging.DEBUG)
        debug_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(debug_handler)

        # 设置特定模块的日志级别
        logging.getLogger('uvicorn').setLevel(logging.WARNING)
        logging.getLogger('uvicorn.access').setLevel(logging.WARNING)
        logging.getLogger('fastapi').setLevel(logging.WARNING)

        # 记录日志配置完成
        logger = logging.getLogger(__name__)
        logger.info("统一日志配置已完成")
        logger.info(f"日志文件位置: {log_dir.absolute()}")
        logger.info("日志级别: 控制台(INFO+), 应用日志(INFO+), 错误日志(ERROR+), 调试日志(ALL)")

        return True

    @classmethod
    def get_oracle_config(cls):
        """获取Oracle配置"""
        return {
            'host': cls.ORACLE_HOST,
            'port': cls.ORACLE_PORT,
            'user': cls.ORACLE_USER,
            'password': cls.ORACLE_PASSWORD,
            'service_name': cls.ORACLE_SERVICE_NAME
        }

    @classmethod
    def get_current_time(cls):
        """获取当前时间（+8时区）"""
        return datetime.now(cls.TIMEZONE)

    @classmethod
    def format_time(cls, dt=None):
        """格式化时间为ISO字符串（+8时区）"""
        if dt is None:
            dt = cls.get_current_time()
        elif dt.tzinfo is None:
            # 如果没有时区信息，假设为+8时区
            dt = dt.replace(tzinfo=cls.TIMEZONE)
        return dt.isoformat()

    @classmethod
    def get_mysql_timezone_config(cls):
        """获取MySQL时区配置"""
        config = cls.get_mysql_config()
        config['init_command'] = "SET time_zone='+08:00'"
        return config

    @classmethod
    def get_postgres_timezone_config(cls):
        """获取PostgreSQL时区配置"""
        config = cls.get_postgres_config()
        config['options'] = "-c timezone=Asia/Shanghai"
        return config
