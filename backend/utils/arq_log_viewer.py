#!/usr/bin/env python3
"""
ARQ实时日志查看器
提供实时查看ARQ Worker日志的功能
"""

import os
import sys
import time
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
import threading
import queue
import signal

class ARQLogViewer:
    """ARQ日志查看器"""
    
    def __init__(self):
        self.log_dir = Path("logs")
        self.worker_log_dir = self.log_dir / "worker"
        self.startup_log_dir = self.log_dir / "startup"
        self.app_log_dir = self.log_dir / "app"
        self.debug_log_dir = self.log_dir / "debug"
        self.error_log_dir = self.log_dir / "error"
        
        self.is_running = True
        self.log_queue = queue.Queue()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在退出...")
        self.is_running = False
        sys.exit(0)
    
    def get_latest_log_file(self, log_type: str = "worker") -> Path:
        """获取最新的日志文件"""
        log_dirs = {
            "worker": self.worker_log_dir,
            "startup": self.startup_log_dir,
            "app": self.app_log_dir,
            "debug": self.debug_log_dir,
            "error": self.error_log_dir
        }
        
        log_dir = log_dirs.get(log_type, self.worker_log_dir)
        
        if not log_dir.exists():
            print(f"❌ 日志目录不存在: {log_dir}")
            return None
        
        # 获取最新的日志文件
        log_files = list(log_dir.glob("*.log"))
        if not log_files:
            print(f"❌ 在 {log_dir} 中没有找到日志文件")
            return None
        
        # 按修改时间排序，获取最新的
        latest_file = max(log_files, key=lambda f: f.stat().st_mtime)
        return latest_file
    
    def list_log_files(self, log_type: str = "worker", limit: int = 10):
        """列出日志文件"""
        log_dirs = {
            "worker": self.worker_log_dir,
            "startup": self.startup_log_dir,
            "app": self.app_log_dir,
            "debug": self.debug_log_dir,
            "error": self.error_log_dir
        }
        
        log_dir = log_dirs.get(log_type, self.worker_log_dir)
        
        if not log_dir.exists():
            print(f"❌ 日志目录不存在: {log_dir}")
            return []
        
        log_files = list(log_dir.glob("*.log"))
        if not log_files:
            print(f"❌ 在 {log_dir} 中没有找到日志文件")
            return []
        
        # 按修改时间排序
        log_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
        
        print(f"\n📁 {log_type.upper()} 日志文件列表 (最新 {limit} 个):")
        print("-" * 80)
        
        for i, log_file in enumerate(log_files[:limit]):
            file_size = log_file.stat().st_size
            file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
            
            print(f"{i+1:2d}. {log_file.name}")
            print(f"    大小: {file_size:,} bytes")
            print(f"    修改时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
        
        return log_files[:limit]
    
    def tail_log_file(self, log_file: Path, lines: int = 50):
        """显示日志文件的最后几行"""
        if not log_file.exists():
            print(f"❌ 日志文件不存在: {log_file}")
            return
        
        try:
            # 使用tail命令显示最后几行
            result = subprocess.run(['tail', '-n', str(lines), str(log_file)], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print(f"\n📄 {log_file.name} 最后 {lines} 行:")
                print("=" * 80)
                print(result.stdout)
                print("=" * 80)
            else:
                print(f"❌ 读取日志文件失败: {result.stderr}")
                
        except Exception as e:
            print(f"❌ 读取日志文件出错: {e}")
    
    def follow_log_file(self, log_file: Path):
        """实时跟踪日志文件"""
        if not log_file.exists():
            print(f"❌ 日志文件不存在: {log_file}")
            return
        
        print(f"\n🔍 实时跟踪日志文件: {log_file.name}")
        print("按 Ctrl+C 退出")
        print("=" * 80)
        
        try:
            # 使用tail -f命令实时跟踪
            process = subprocess.Popen(['tail', '-f', str(log_file)], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE,
                                     text=True, 
                                     encoding='utf-8')
            
            while self.is_running:
                line = process.stdout.readline()
                if line:
                    # 添加时间戳和颜色
                    timestamp = datetime.now().strftime('%H:%M:%S')
                    print(f"[{timestamp}] {line.rstrip()}")
                else:
                    time.sleep(0.1)
                    
        except KeyboardInterrupt:
            print("\n⏹️ 停止跟踪日志")
        except Exception as e:
            print(f"❌ 跟踪日志出错: {e}")
        finally:
            if 'process' in locals():
                process.terminate()
    
    def search_logs(self, pattern: str, log_type: str = "worker", context_lines: int = 3):
        """在日志中搜索模式"""
        log_dirs = {
            "worker": self.worker_log_dir,
            "startup": self.startup_log_dir,
            "app": self.app_log_dir,
            "debug": self.debug_log_dir,
            "error": self.error_log_dir
        }
        
        log_dir = log_dirs.get(log_type, self.worker_log_dir)
        
        if not log_dir.exists():
            print(f"❌ 日志目录不存在: {log_dir}")
            return
        
        log_files = list(log_dir.glob("*.log"))
        if not log_files:
            print(f"❌ 在 {log_dir} 中没有找到日志文件")
            return
        
        print(f"\n🔍 在 {log_type} 日志中搜索: '{pattern}'")
        print("=" * 80)
        
        found_count = 0
        for log_file in log_files:
            try:
                # 使用grep搜索
                result = subprocess.run(['grep', '-n', '-i', f'-C{context_lines}', pattern, str(log_file)], 
                                      capture_output=True, text=True, encoding='utf-8')
                
                if result.returncode == 0 and result.stdout:
                    print(f"\n📄 {log_file.name}:")
                    print("-" * 40)
                    print(result.stdout)
                    found_count += 1
                    
            except Exception as e:
                print(f"❌ 搜索文件 {log_file.name} 出错: {e}")
        
        if found_count == 0:
            print(f"❌ 没有找到包含 '{pattern}' 的日志")
        else:
            print(f"\n✅ 在 {found_count} 个文件中找到匹配项")
    
    def show_log_summary(self):
        """显示日志摘要"""
        print("\n📊 ARQ日志摘要")
        print("=" * 80)
        
        log_types = ["worker", "startup", "app", "debug", "error"]
        
        for log_type in log_types:
            log_dirs = {
                "worker": self.worker_log_dir,
                "startup": self.startup_log_dir,
                "app": self.app_log_dir,
                "debug": self.debug_log_dir,
                "error": self.error_log_dir
            }
            
            log_dir = log_dirs[log_type]
            
            if log_dir.exists():
                log_files = list(log_dir.glob("*.log"))
                total_size = sum(f.stat().st_size for f in log_files)
                
                print(f"\n📁 {log_type.upper()} 日志:")
                print(f"  文件数量: {len(log_files)}")
                print(f"  总大小: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")
                
                if log_files:
                    latest_file = max(log_files, key=lambda f: f.stat().st_mtime)
                    latest_time = datetime.fromtimestamp(latest_file.stat().st_mtime)
                    print(f"  最新文件: {latest_file.name}")
                    print(f"  最新时间: {latest_time.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                print(f"\n📁 {log_type.upper()} 日志: 目录不存在")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n🎯 ARQ日志查看器 - 交互模式")
        print("=" * 80)
        
        while self.is_running:
            print("\n可用命令:")
            print("1. list [type] [limit] - 列出日志文件")
            print("2. tail [type] [lines] - 显示最后几行")
            print("3. follow [type] - 实时跟踪日志")
            print("4. search <pattern> [type] - 搜索日志")
            print("5. summary - 显示日志摘要")
            print("6. quit - 退出")
            print("\n日志类型: worker, startup, app, debug, error")
            
            try:
                command = input("\n请输入命令: ").strip().split()
                if not command:
                    continue
                
                cmd = command[0].lower()
                
                if cmd == 'quit' or cmd == 'q':
                    break
                elif cmd == 'list':
                    log_type = command[1] if len(command) > 1 else "worker"
                    limit = int(command[2]) if len(command) > 2 else 10
                    self.list_log_files(log_type, limit)
                elif cmd == 'tail':
                    log_type = command[1] if len(command) > 1 else "worker"
                    lines = int(command[2]) if len(command) > 2 else 50
                    log_file = self.get_latest_log_file(log_type)
                    if log_file:
                        self.tail_log_file(log_file, lines)
                elif cmd == 'follow':
                    log_type = command[1] if len(command) > 1 else "worker"
                    log_file = self.get_latest_log_file(log_type)
                    if log_file:
                        self.follow_log_file(log_file)
                elif cmd == 'search':
                    if len(command) < 2:
                        print("❌ 请提供搜索模式")
                        continue
                    pattern = command[1]
                    log_type = command[2] if len(command) > 2 else "worker"
                    self.search_logs(pattern, log_type)
                elif cmd == 'summary':
                    self.show_log_summary()
                else:
                    print("❌ 未知命令")
                    
            except KeyboardInterrupt:
                print("\n⏹️ 退出交互模式")
                break
            except Exception as e:
                print(f"❌ 命令执行出错: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ARQ日志查看器')
    parser.add_argument('--type', default='worker', choices=['worker', 'startup', 'app', 'debug', 'error'],
                       help='日志类型')
    parser.add_argument('--list', action='store_true', help='列出日志文件')
    parser.add_argument('--tail', type=int, metavar='LINES', help='显示最后几行')
    parser.add_argument('--follow', action='store_true', help='实时跟踪日志')
    parser.add_argument('--search', metavar='PATTERN', help='搜索模式')
    parser.add_argument('--summary', action='store_true', help='显示日志摘要')
    parser.add_argument('--interactive', action='store_true', help='交互模式')
    
    args = parser.parse_args()
    
    viewer = ARQLogViewer()
    
    try:
        if args.interactive:
            viewer.interactive_mode()
        elif args.list:
            viewer.list_log_files(args.type)
        elif args.tail:
            log_file = viewer.get_latest_log_file(args.type)
            if log_file:
                viewer.tail_log_file(log_file, args.tail)
        elif args.follow:
            log_file = viewer.get_latest_log_file(args.type)
            if log_file:
                viewer.follow_log_file(log_file)
        elif args.search:
            viewer.search_logs(args.search, args.type)
        elif args.summary:
            viewer.show_log_summary()
        else:
            # 默认显示最新的worker日志
            log_file = viewer.get_latest_log_file(args.type)
            if log_file:
                viewer.tail_log_file(log_file, 50)
    
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序出错: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
