#!/usr/bin/env python3
"""
AI SQL PCAP Analyzer 统一启动脚本
包含完整的FastAPI应用和Arq工作器，以及依赖检查和环境设置
优化启动顺序：API服务优先启动，其他服务后台启动，启动日志单独管理
"""

import os
import asyncio
import logging
import signal
import sys
import subprocess
import time
import threading
from multiprocessing import Process
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入环境检测器
from utils.environment_detector import log_environment_info

# FastAPI相关导入
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# 设置Oracle Instant Client环境变量（跨平台兼容）
def setup_oracle_environment():
    """设置Oracle环境变量（跨平台）"""
    import platform

    system = platform.system().lower()

    if system == 'darwin':  # macOS
        oracle_lib_path = "/Users/<USER>/Desktop/qz_code/ai_sql_pcap/oracle_device/instantclient_19_8"
        lib_path_var = 'DYLD_LIBRARY_PATH'
    elif system == 'linux':  # Linux
        # Linux上的常见Oracle客户端路径
        possible_paths = [
            "/opt/oracle/instantclient_19_8",
            "/usr/lib/oracle/19.8/client64/lib",
            "/opt/oracle/instantclient",
            "/usr/local/oracle/instantclient_19_8",
            "./oracle_device/instantclient_19_8",
            os.path.join(os.path.dirname(__file__), "..", "oracle_device", "instantclient_19_8")
        ]
        oracle_lib_path = None
        for path in possible_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path):
                oracle_lib_path = abs_path
                break

        if not oracle_lib_path:
            oracle_lib_path = "/opt/oracle/instantclient_19_8"

        lib_path_var = 'LD_LIBRARY_PATH'
    else:  # Windows或其他
        oracle_lib_path = "./oracle_device/instantclient_19_8"
        lib_path_var = 'PATH'

    # 使用基础日志记录，因为startup_logger还未定义
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"🔍 检测到操作系统: {system}")
    logger.info(f"🔍 Oracle客户端路径: {oracle_lib_path}")

    if os.path.exists(oracle_lib_path):
        # 设置库路径环境变量
        current_lib_path = os.environ.get(lib_path_var, '')
        if oracle_lib_path not in current_lib_path:
            new_lib_path = f"{oracle_lib_path}:{current_lib_path}" if current_lib_path else oracle_lib_path
            os.environ[lib_path_var] = new_lib_path
            os.putenv(lib_path_var, new_lib_path)

        # 设置ORACLE_HOME
        os.environ['ORACLE_HOME'] = oracle_lib_path
        os.putenv('ORACLE_HOME', oracle_lib_path)

        logger.info(f"✅ Oracle环境变量已自动设置:")
        logger.info(f"   ORACLE_HOME={oracle_lib_path}")
        logger.info(f"   {lib_path_var}={os.environ[lib_path_var]}")
        return True
    else:
        logger.warning(f"⚠️  Oracle Instant Client路径不存在: {oracle_lib_path}")
        if system == 'linux':
            logger.info("💡 Linux用户请安装Oracle Instant Client:")
            logger.info("   sudo mkdir -p /opt/oracle")
            logger.info("   # 下载并解压Oracle Instant Client到 /opt/oracle/instantclient_19_8")
        return False

def check_dependencies():
    """检查系统依赖"""
    import logging
    logger = logging.getLogger(__name__)
    logger.info("🔍 检查系统依赖...")

    # 检查Python版本
    if sys.version_info < (3, 8):
        logger.error("❌ 需要Python 3.8或更高版本")
        return False
    else:
        logger.info(f"✅ Python版本: {sys.version}")

    # 检查tcpdump
    try:
        result = subprocess.run(['tcpdump', '--version'],
                              capture_output=True, timeout=5)
        logger.info("✅ tcpdump可用")
    except (FileNotFoundError, subprocess.TimeoutExpired):
        logger.warning("⚠️  tcpdump不可用，抓包功能可能受限")

    return True

def install_dependencies():
    """安装Python依赖"""
    import logging
    logger = logging.getLogger(__name__)
    logger.info("📦 检查并安装Python依赖...")
    try:
        # 检查requirements.txt是否存在
        if not Path('requirements.txt').exists():
            logger.warning("⚠️  requirements.txt不存在，跳过依赖安装")
            return True

        result = subprocess.run(
            [sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
            capture_output=True, text=True
        )
        if result.returncode != 0:
            logger.warning("⚠️  依赖安装出现问题，但不阻断启动。输出如下：")
            logger.warning(result.stdout)
            logger.warning(result.stderr)
            return True

        logger.info("✅ Python依赖检查完成")
        return True
    except Exception as e:
        logger.warning(f"⚠️  依赖安装阶段异常（已忽略继续启动）: {e}")
        return True

def create_directories():
    """创建必要的目录"""
    dirs = ['captures', 'logs']
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
    import logging
    logger = logging.getLogger(__name__)
    logger.info("✅ 必要目录创建完成")

# 设置Oracle环境变量
setup_oracle_environment()

# 设置统一的日志配置
from utils.config import Config
Config.setup_logging()

# 启动日志管理器
class StartupLogger:
    """启动日志管理器"""

    def __init__(self):
        self.log_dir = Path("logs/startup")
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # 创建启动日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.log_dir / f"startup_{timestamp}.log"

        # 配置启动日志记录器
        self.logger = logging.getLogger("startup")
        self.logger.setLevel(logging.INFO)

        # 文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        self.logger.info(f"启动日志记录器初始化完成，日志文件: {self.log_file}")

    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)

    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)

    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)

    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)

# 全局启动日志记录器
startup_logger = StartupLogger()

# 配置应用日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ==================== FastAPI应用定义 ====================

# 只导入必要的基础模块，其他服务延迟导入
from utils.config import Config

# 创建FastAPI应用
app = FastAPI(title="AI SQL PCAP Analyzer", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # 开发环境
        "http://localhost:3001",  # 开发环境
        "http://localhost:3002",  # 开发环境
        "http://localhost:8080",  # 本地测试
        "http://************:8080",  # 生产环境
        "http://************:3000",   # 开发环境
        "http://************:3001",    # 开发环境
        "http://************:3002"     # 开发环境
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 设置全局异常处理中间件
from middleware.exception_middleware import setup_exception_middleware
setup_exception_middleware(app)

# API路由将在后台初始化时动态加载

# 请求模型
class QueryRequest(BaseModel):
    query: str
    capture_packets: bool = True
    config_id: Optional[int] = None  # 指定使用的数据库配置ID

class QueryResponse(BaseModel):
    success: bool
    sql_query: Optional[str] = None
    result: Optional[Any] = None
    packet_file: Optional[str] = None
    error: Optional[str] = None

# 服务实例 - 延迟初始化
ai_service = None
db_config_service = None
system_management_service = None
mysql_service = None
mysql_protocol_service = None
packet_service = None
postgres_service = None

async def get_mysql_service():
    """按需获取MySQL服务实例"""
    global mysql_service
    if mysql_service is None:
        from services.mysql_service import MySQLService
        mysql_config = Config.get_mysql_config()
        mysql_service = MySQLService(**mysql_config)
        await mysql_service.initialize()
    return mysql_service

async def get_postgres_service():
    """按需获取PostgreSQL服务实例"""
    global postgres_service
    if postgres_service is None:
        from services.postgres_service import PostgresService
        postgres_config = Config.get_postgres_config()
        postgres_service = PostgresService(**postgres_config)
        await postgres_service.initialize()
    return postgres_service

async def get_ai_service():
    """按需获取AI服务实例"""
    global ai_service
    if ai_service is None:
        from services.ai_service import AIService
        ai_service = AIService()
    return ai_service

async def get_packet_service():
    """按需获取抓包服务实例"""
    global packet_service
    if packet_service is None:
        from services.remote_packet_capture_service import RemotePacketCaptureService
        packet_service = RemotePacketCaptureService(capture_dir=Config.CAPTURE_DIR)
    return packet_service

async def get_mysql_protocol_service():
    """按需获取MySQL协议服务实例"""
    global mysql_protocol_service
    if mysql_protocol_service is None:
        from services.mysql_protocol_service import MySQLProtocolService
        mysql_protocol_service = MySQLProtocolService()
    return mysql_protocol_service

# 后台服务状态管理
class BackgroundServiceManager:
    """后台服务管理器"""

    def __init__(self):
        self.services_status = {
            'redis': False,
            'task_management': False,
            'database_config': False,
            'capture_file': False,
            'server_config': False,
            'docker_image': False,
            'system_management': False
        }
        self.initialization_complete = False
        self.initialization_lock = asyncio.Lock()

    async def initialize_background_services(self):
        """后台初始化所有服务"""
        async with self.initialization_lock:
            if self.initialization_complete:
                return

            startup_logger.info("开始后台初始化服务...")

            # 1. 首先加载API路由（这些是轻量级的）
            try:
                await self._load_api_routes()
                startup_logger.info("✅ API路由加载成功")
            except Exception as e:
                startup_logger.error(f"❌ API路由加载失败: {e}")

            # 2. 初始化Redis连接
            try:
                from config.redis_config import redis_manager
                await redis_manager.initialize()
                self.services_status['redis'] = True
                startup_logger.info("✅ Redis连接初始化成功")
            except Exception as e:
                startup_logger.error(f"❌ Redis初始化失败: {e}")

            # 3. 初始化任务管理服务
            try:
                from services.arq_task_management_service import arq_task_management_service as task_management_service
                await task_management_service.initialize()
                self.services_status['task_management'] = True
                startup_logger.info("✅ 任务管理服务初始化成功")
            except Exception as e:
                startup_logger.error(f"❌ 任务管理服务初始化失败: {e}")

            # 4. 初始化数据库配置服务
            try:
                from services.database_config_service import DatabaseConfigService
                global db_config_service
                db_config_service = DatabaseConfigService()
                await db_config_service.initialize()
                self.services_status['database_config'] = True
                startup_logger.info("✅ 数据库配置服务初始化成功")
            except Exception as e:
                startup_logger.error(f"❌ 数据库配置服务初始化失败: {e}")

            # 5. 初始化抓包文件服务
            try:
                from services.capture_file_service import capture_file_service
                await capture_file_service.initialize()
                self.services_status['capture_file'] = True
                startup_logger.info("✅ 抓包文件服务初始化成功")
            except Exception as e:
                startup_logger.error(f"❌ 抓包文件服务初始化失败: {e}")

            # 6. 初始化服务器配置服务
            try:
                from services.server_config_service import server_config_service
                await server_config_service.initialize()
                self.services_status['server_config'] = True
                startup_logger.info("✅ 服务器配置服务初始化成功")
            except Exception as e:
                startup_logger.error(f"❌ 服务器配置服务初始化失败: {e}")

            # 7. 初始化Docker镜像服务
            try:
                from services.docker_image_service import docker_image_service
                await docker_image_service.initialize()
                self.services_status['docker_image'] = True
                startup_logger.info("✅ Docker镜像服务初始化成功")
            except Exception as e:
                startup_logger.error(f"❌ Docker镜像服务初始化失败: {e}")

            # 8. 初始化系统管理服务的默认部署配置
            try:
                from services.system_management_service import SystemManagementService, DatabaseDeployment
                global system_management_service
                system_management_service = SystemManagementService()

                # 添加测试数据库部署配置
                test_deployment = DatabaseDeployment(
                    name="test_mysql",
                    host="**************",
                    port=3306,
                    user="root",
                    password="QZ@1005#1005",
                    database="test",
                    deployment_type="direct",
                    ssh_host="**************",
                    ssh_user="root",
                    ssh_password="QZ@1005#1005"
                )
                system_management_service.add_deployment(test_deployment)
                self.services_status['system_management'] = True
                startup_logger.info("✅ 系统管理服务初始化成功")
            except Exception as e:
                startup_logger.error(f"❌ 系统管理服务初始化失败: {e}")

            # 9. 设置系统管理服务并包含API路由
            try:
                from api.system_management_api import set_system_management_service, router as system_router
                set_system_management_service(system_management_service)
                app.include_router(system_router, prefix="/api")
                startup_logger.info("✅ 系统管理API初始化成功")
            except Exception as e:
                startup_logger.error(f"❌ 系统管理API初始化失败: {e}")

            self.initialization_complete = True
            startup_logger.info("🎉 后台服务初始化完成")

    async def _load_api_routes(self):
        """加载API路由"""
        # 分模块、逐个容错加载，避免某个依赖缺失导致全部路由失效
        def safe_include(import_path: str, var_name: str = "router", prefix: str = "/api"):
            try:
                module = __import__(import_path, fromlist=[var_name])
                router_obj = getattr(module, var_name)
                if prefix:
                    app.include_router(router_obj, prefix=prefix)
                else:
                    app.include_router(router_obj)
                startup_logger.info(f"✅ 路由加载成功: {import_path}")
            except Exception as ie:
                startup_logger.error(f"❌ 路由加载失败({import_path}): {ie}")

        # 基础路由优先
        safe_include('api.database_api')
        safe_include('api.download_api')
        safe_include('api.server_config_api')
        safe_include('api.task_api')
        safe_include('api.mysql_mcp_api')
        safe_include('api.test_case_management_api')
        safe_include('api.execution_log_api')
        safe_include('api.test_case_execution_api', prefix=None)  # 自身含/api
        safe_include('api.gaussdb_api')
        safe_include('api.smart_capture_api')
        safe_include('api.tcpdump_management_api', prefix="/api")
        safe_include('api.bulk_data_api')

        # 新增：步骤校验相关路由（关键，不受其他可选依赖影响）
        safe_include('api.step_validation_api', prefix=None)
        safe_include('api.step_validation_dashboard_api', prefix=None)

        # 可选依赖的路由（缺失不影响整体）
        safe_include('api.postgres_api')
        safe_include('api.oracle_api')
        safe_include('api.mongo_api')

    def get_services_status(self):
        """获取服务状态"""
        return {
            'services': self.services_status,
            'initialization_complete': self.initialization_complete
        }

    async def wait_for_services(self, timeout: int = 30):
        """等待服务初始化完成"""
        start_time = time.time()
        while not self.initialization_complete and (time.time() - start_time) < timeout:
            await asyncio.sleep(0.5)
        return self.initialization_complete

# 全局后台服务管理器
background_service_manager = BackgroundServiceManager()

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化 - 只初始化核心API服务"""
    startup_logger.info("🚀 启动AI SQL PCAP Analyzer API服务...")

    # 启动tcpdump进程管理器
    try:
        from services.tcpdump_process_manager import tcpdump_manager
        await tcpdump_manager.start_manager()
        startup_logger.info("🔧 tcpdump进程管理器已启动")
    except Exception as e:
        startup_logger.error(f"启动tcpdump进程管理器失败: {e}")

    # 数据库服务改为按需初始化，不在启动时强制连接
    startup_logger.info("📊 数据库服务将按需初始化")

    # 在后台线程中初始化其他服务
    def background_init():
        """后台初始化函数"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(background_service_manager.initialize_background_services())
        except Exception as e:
            startup_logger.error(f"后台服务初始化异常: {e}")
        finally:
            loop.close()

    # 启动后台初始化线程
    background_thread = threading.Thread(target=background_init, daemon=True)
    background_thread.start()

    startup_logger.info("✅ API服务启动完成，后台服务正在初始化中...")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("Shutting down AI SQL PCAP Analyzer...")

    # 关闭数据库服务
    if mysql_service:
        try:
            await mysql_service.close()
        except Exception as e:
            logger.error(f"Error closing MySQL service: {e}")

    if postgres_service:
        try:
            await postgres_service.close()
        except Exception as e:
            logger.error(f"Error closing PostgreSQL service: {e}")

    # 关闭抓包服务
    if packet_service:
        try:
            await packet_service.stop_capture()
            await packet_service.close()
        except Exception as e:
            logger.error(f"Error closing packet service: {e}")

    # 关闭tcpdump进程管理器
    try:
        from services.tcpdump_process_manager import tcpdump_manager
        await tcpdump_manager.stop_manager()
        logger.info("tcpdump进程管理器已关闭")
    except Exception as e:
        logger.error(f"关闭tcpdump进程管理器失败: {e}")

    # 关闭Redis连接
    try:
        from config.redis_config import redis_manager
        await redis_manager.close()
        logger.info("Redis connection closed")
    except Exception as e:
        logger.error(f"Redis shutdown error: {e}")

    logger.info("Application shutdown complete")

@app.get("/")
async def root():
    return {"message": "AI SQL PCAP Analyzer API"}

@app.get("/api/health")
async def health_check():
    """健康检查"""
    services_status = background_service_manager.get_services_status()

    # 安全地检查抓包服务状态
    packet_capture_status = False
    try:
        if packet_service:
            packet_capture_status = packet_service.is_capturing()
    except Exception:
        packet_capture_status = False

    return {
        "status": "healthy",
        "message": "AI SQL PCAP Analyzer is running",
        "services": {
            "mysql_service": "on-demand",
            "postgres_service": "on-demand",
            "packet_capture": packet_capture_status,
            "ai_service": "on-demand",
            **services_status['services']
        },
        "background_initialization_complete": services_status['initialization_complete']
    }

@app.get("/api/startup/status")
async def get_startup_status():
    """获取启动状态"""
    return background_service_manager.get_services_status()

@app.post("/api/startup/wait")
async def wait_for_startup(timeout: int = 30):
    """等待启动完成"""
    success = await background_service_manager.wait_for_services(timeout)
    return {
        "success": success,
        "message": "服务初始化完成" if success else f"等待超时({timeout}秒)",
        "services": background_service_manager.get_services_status()
    }

@app.post("/api/query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    """处理自然语言查询"""
    try:
        logger.info(f"Processing query: {request.query}")

        # 检查后台服务是否初始化完成，如果没有则等待
        if not background_service_manager.initialization_complete:
            startup_logger.info("等待后台服务初始化完成...")
            await background_service_manager.wait_for_services(timeout=10)
            if not background_service_manager.initialization_complete:
                startup_logger.warning("后台服务初始化未完成，但继续处理请求")

        # 获取服务实例（延迟加载）
        current_ai_service = await get_ai_service()
        current_packet_service = await get_packet_service()

        # 1. 确定使用的数据库配置
        if request.config_id:
            if not db_config_service:
                raise Exception("Database config service not initialized")
            db_config = await db_config_service.get_config(request.config_id)
            if not db_config:
                raise Exception(f"Database configuration ID '{request.config_id}' not found")
            logger.info(f"Using database configuration: {db_config.name} (ID: {request.config_id})")
        else:
            if not db_config_service:
                raise Exception("Database config service not initialized")
            db_config = await db_config_service.get_default_config()
            if not db_config:
                raise Exception("No default database configuration found")
            logger.info(f"Using default database configuration: {db_config.name} (ID: {db_config.id})")

        # 2. 使用AI解析自然语言为SQL或协议命令
        parsed_result = await current_ai_service.parse_natural_language_to_sql(request.query)
        logger.info(f"Generated result: {parsed_result}")

        packet_file = None

        # 3. 如果需要抓包，启动抓包服务（传递目标主机和端口）
        if request.capture_packets:
            packet_file = await current_packet_service.start_capture(
                target_host=db_config.host,
                target_port=db_config.port
            )
            logger.info(f"Started packet capture: {packet_file}")

        # 4. 创建数据库配置字典
        db_config_dict = {
            'host': db_config.host,
            'port': db_config.port,
            'user': db_config.user,
            'password': db_config.password,
            'database': db_config.database_name
        }

        # 5. 判断是SQL还是协议命令并执行
        if parsed_result.startswith('PROTOCOL:'):
            # 协议命令处理逻辑
            protocol_content = parsed_result[9:]  # 去掉 "PROTOCOL:" 前缀

            if ':' in protocol_content:
                command_parts = protocol_content.split(':')
                command_name = command_parts[0]
                command_args = command_parts[1:]
            else:
                command_parts = protocol_content.split()
                command_name = command_parts[0]
                command_args = command_parts[1:]

            logger.info(f"Executing MySQL native protocol command: {command_name} with args: {command_args}")

            from services.mysql_native_command_service import MySQLNativeCommandService
            native_command_service = MySQLNativeCommandService()
            try:
                kwargs = {}
                if command_name == 'KILL_THREAD' and command_args:
                    kwargs['thread_id'] = int(command_args[0])
                elif command_name in ['REFRESH_PRIVILEGES', 'REFRESH']:
                    command_name = 'REFRESH'

                result = await native_command_service.execute_protocol_command(command_name, **kwargs)

                if command_name in ['SHUTDOWN', 'KILL_THREAD']:
                    result['restart_notice'] = f"⚠️ 危险命令 {command_name} 已执行，如果MySQL服务停止，请手动重启服务器MySQL服务"

            except Exception as e:
                logger.error(f"Native protocol command {command_name} failed: {str(e)}")
                result = {
                    'success': False,
                    'command': command_name,
                    'error_message': str(e),
                    'response_type': 'ERROR'
                }
            finally:
                native_command_service.close()

            sql_query = f"PROTOCOL:{command_name}"
        else:
            # 标准SQL查询
            sql_query = parsed_result

            if not request.capture_packets:
                logger.info("SQL query parsed only (not executed)")
                return QueryResponse(
                    success=True,
                    sql_query=sql_query,
                    result={'message': 'SQL parsed successfully (not executed)', 'response_type': 'PARSE_ONLY'},
                    packet_file=None
                )
            else:
                from services.mysql_service import MySQLService
                query_mysql_service = MySQLService(**db_config_dict)
                result = await query_mysql_service.execute_query(sql_query)
                await query_mysql_service.close()
                logger.info("SQL query executed successfully")

        # 停止抓包
        if request.capture_packets:
            final_packet_file = await current_packet_service.stop_capture()
            if final_packet_file:
                packet_file = final_packet_file
            logger.info(f"Stopped packet capture, final file: {packet_file}")

        return QueryResponse(
            success=True,
            sql_query=sql_query,
            result=result,
            packet_file=packet_file
        )

    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        if request.capture_packets:
            current_packet_service = await get_packet_service()
            await current_packet_service.stop_capture()

        return QueryResponse(
            success=False,
            error=str(e)
        )

@app.get("/api/capture/status")
async def get_capture_status():
    """获取抓包状态"""
    try:
        current_packet_service = await get_packet_service()
        return {
            "is_capturing": current_packet_service.is_capturing(),
            "current_file": current_packet_service.get_current_file()
        }
    except Exception as e:
        return {
            "is_capturing": False,
            "current_file": None,
            "error": str(e)
        }

@app.post("/api/capture/start")
async def start_capture():
    """手动启动抓包"""
    try:
        current_packet_service = await get_packet_service()
        packet_file = await current_packet_service.start_capture()
        return {"success": True, "packet_file": packet_file}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/capture/stop")
async def stop_capture():
    """手动停止抓包"""
    try:
        current_packet_service = await get_packet_service()
        await current_packet_service.stop_capture()
        return {"success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/databases")
async def get_databases():
    """获取数据库列表"""
    try:
        service = await get_mysql_service()
        databases = await service.get_databases()
        return {"databases": databases}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/tables/{database}")
async def get_tables(database: str):
    """获取指定数据库的表列表"""
    try:
        service = await get_mysql_service()
        tables = await service.get_tables(database)
        return {"tables": tables}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 工作器和启动函数 ====================

def get_python_executable():
    """获取正确的Python可执行文件路径"""
    import os

    # 在Docker容器中，直接使用系统Python
    if os.path.exists('/.dockerenv'):
        return sys.executable

    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # 在虚拟环境中，使用虚拟环境的Python
        venv_python = os.path.join(sys.prefix, 'bin', 'python')
        if os.path.exists(venv_python):
            return venv_python

    # 检查当前目录的venv
    script_dir = os.path.dirname(os.path.abspath(__file__))
    venv_python = os.path.join(script_dir, 'venv', 'bin', 'python')
    if os.path.exists(venv_python):
        return venv_python

    # 回退到系统Python
    return sys.executable

def cleanup_existing_workers():
    """清理现有的worker进程"""
    try:
        startup_logger.info("🧹 检查并清理现有的worker进程...")

        # 多种方式查找ARQ worker进程
        patterns = [
            'arq.*WorkerSettings',
            'python.*arq.*WorkerSettings',
            'python.*-m.*arq',
            'arq.*worker',
            'tasks.worker.WorkerSettings'
        ]

        all_pids = set()

        for pattern in patterns:
            try:
                result = subprocess.run(['pgrep', '-f', pattern], capture_output=True, text=True)
                if result.returncode == 0:
                    pids = result.stdout.strip().split('\n')
                    pids = [pid.strip() for pid in pids if pid.strip()]
                    all_pids.update(pids)
                    if pids:
                        startup_logger.info(f"通过模式 '{pattern}' 发现进程: {pids}")
            except Exception as e:
                startup_logger.debug(f"使用模式 '{pattern}' 查找进程失败: {e}")

        # 额外使用ps命令查找，但排除当前进程
        current_pid = str(os.getpid())
        try:
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if any(keyword in line.lower() for keyword in ['arq', 'worker', 'tasks.worker']):
                        if 'python' in line.lower() and ('arq' in line.lower() or 'worker' in line.lower()):
                            # 排除包含start_with_worker.py的进程（当前启动脚本）
                            if 'start_with_worker.py' in line:
                                continue
                            parts = line.split()
                            if len(parts) > 1:
                                try:
                                    pid = parts[1]
                                    int(pid)  # 验证是否为数字
                                    # 排除当前进程
                                    if pid != current_pid:
                                        all_pids.add(pid)
                                        startup_logger.info(f"通过ps命令发现ARQ相关进程: PID={pid}")
                                except ValueError:
                                    pass
        except Exception as e:
            startup_logger.debug(f"使用ps命令查找进程失败: {e}")

        # 过滤掉当前进程
        current_pid = str(os.getpid())
        all_pids.discard(current_pid)

        if all_pids:
            startup_logger.info(f"发现 {len(all_pids)} 个ARQ相关进程: {list(all_pids)}")

            for pid in all_pids:
                try:
                    pid_int = int(pid)
                    # 再次确认不是当前进程
                    if pid_int == os.getpid():
                        startup_logger.info(f"跳过当前进程: {pid}")
                        continue

                    startup_logger.info(f"终止ARQ进程: {pid}")

                    # 尝试优雅终止
                    os.kill(pid_int, signal.SIGTERM)
                    time.sleep(2)

                    # 检查进程是否还在运行
                    try:
                        os.kill(pid_int, 0)  # 检查进程是否存在
                        # 如果还在运行，强制终止
                        startup_logger.warning(f"强制终止ARQ进程: {pid}")
                        os.kill(pid_int, signal.SIGKILL)
                        time.sleep(1)
                    except ProcessLookupError:
                        # 进程已经终止
                        pass

                except (ValueError, ProcessLookupError) as e:
                    startup_logger.warning(f"清理进程 {pid} 失败: {e}")

            startup_logger.info("✅ ARQ相关进程清理完成")
        else:
            startup_logger.info("没有发现ARQ相关进程")

    except Exception as e:
        startup_logger.error(f"清理ARQ进程失败: {e}")

def cleanup_port_8000():
    """清理占用8000端口的进程"""
    try:
        startup_logger.info("🧹 检查8000端口占用情况...")

        # 多种方式查找占用8000端口的进程
        pids = set()

        # 方法1: 使用lsof
        try:
            result = subprocess.run(['lsof', '-ti:8000'], capture_output=True, text=True)
            if result.returncode == 0:
                lsof_pids = result.stdout.strip().split('\n')
                lsof_pids = [pid.strip() for pid in lsof_pids if pid.strip()]
                pids.update(lsof_pids)
                if lsof_pids:
                    startup_logger.info(f"lsof发现占用8000端口的进程: {lsof_pids}")
        except Exception as e:
            startup_logger.debug(f"lsof命令失败: {e}")

        # 方法2: 使用netstat
        try:
            result = subprocess.run(['netstat', '-tlnp'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if ':8000 ' in line and 'LISTEN' in line:
                        parts = line.split()
                        for part in parts:
                            if '/' in part:
                                try:
                                    pid = part.split('/')[0]
                                    int(pid)  # 验证是否为数字
                                    pids.add(pid)
                                    startup_logger.info(f"netstat发现占用8000端口的进程: PID={pid}")
                                except ValueError:
                                    pass
        except Exception as e:
            startup_logger.debug(f"netstat命令失败: {e}")

        # 方法3: 使用ss命令
        try:
            result = subprocess.run(['ss', '-tlnp'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if ':8000 ' in line and 'LISTEN' in line:
                        if 'pid=' in line:
                            import re
                            match = re.search(r'pid=(\d+)', line)
                            if match:
                                pid = match.group(1)
                                pids.add(pid)
                                startup_logger.info(f"ss发现占用8000端口的进程: PID={pid}")
        except Exception as e:
            startup_logger.debug(f"ss命令失败: {e}")

        if pids:
            startup_logger.info(f"发现 {len(pids)} 个进程占用8000端口: {list(pids)}")

            for pid in pids:
                try:
                    pid_int = int(pid)
                    startup_logger.info(f"终止占用8000端口的进程: {pid}")

                    # 尝试优雅终止
                    os.kill(pid_int, signal.SIGTERM)
                    time.sleep(2)

                    # 检查进程是否还在运行
                    try:
                        os.kill(pid_int, 0)
                        # 如果还在运行，强制终止
                        startup_logger.warning(f"强制终止进程: {pid}")
                        os.kill(pid_int, signal.SIGKILL)
                        time.sleep(1)
                    except ProcessLookupError:
                        pass

                except (ValueError, ProcessLookupError) as e:
                    startup_logger.warning(f"清理进程 {pid} 失败: {e}")

            startup_logger.info("✅ 8000端口清理完成")
        else:
            startup_logger.info("8000端口没有被占用")

    except Exception as e:
        startup_logger.error(f"清理8000端口失败: {e}")

def start_main_app():
    """启动主应用"""
    try:
        startup_logger.info("🚀 启动主应用...")

        # 直接启动FastAPI应用，不使用子进程
        import uvicorn

        startup_logger.info("=" * 60)
        startup_logger.info("🚀 AI SQL PCAP Analyzer 启动完成")
        startup_logger.info("=" * 60)
        startup_logger.info(f"🌐 后端服务地址: http://{Config.APP_HOST}:{Config.APP_PORT}")
        startup_logger.info(f"📚 API文档地址: http://{Config.APP_HOST}:{Config.APP_PORT}/docs")
        startup_logger.info(f"📊 任务管理页面: http://localhost:3002/#/task-management")
        startup_logger.info(f"📋 启动状态检查: http://{Config.APP_HOST}:{Config.APP_PORT}/api/startup/status")
        startup_logger.info("=" * 60)
        startup_logger.info("💡 API服务已启动，后台服务正在初始化中...")
        startup_logger.info("💡 可通过 /api/startup/status 查看后台服务初始化状态")
        startup_logger.info("=" * 60)

        startup_logger.info("✅ API服务启动完成")

        uvicorn.run(
            app,
            host=Config.APP_HOST,
            port=Config.APP_PORT,
            log_level="info"
        )

    except KeyboardInterrupt:
        startup_logger.info("⏹️ 主应用被用户中断")
    except Exception as e:
        startup_logger.error(f"❌ 主应用启动失败: {e}")
        raise

def start_app_directly():
    """直接启动FastAPI应用（不使用子进程）"""
    try:
        startup_logger.info("🚀 直接启动FastAPI应用...")

        # 直接启动FastAPI应用
        import uvicorn

        startup_logger.info("=" * 60)
        startup_logger.info("🚀 AI SQL PCAP Analyzer 启动完成")
        startup_logger.info("=" * 60)
        startup_logger.info(f"🌐 后端服务地址: http://{Config.APP_HOST}:{Config.APP_PORT}")
        startup_logger.info(f"📚 API文档地址: http://{Config.APP_HOST}:{Config.APP_PORT}/docs")
        startup_logger.info(f"📊 任务管理页面: http://localhost:3002/#/task-management")
        startup_logger.info(f"📋 启动状态检查: http://{Config.APP_HOST}:{Config.APP_PORT}/api/startup/status")
        startup_logger.info("=" * 60)
        startup_logger.info("💡 API服务已启动，后台服务正在初始化中...")
        startup_logger.info("💡 可通过 /api/startup/status 查看后台服务初始化状态")
        startup_logger.info("=" * 60)

        startup_logger.info("✅ API服务启动完成")

        uvicorn.run(
            app,
            host=Config.APP_HOST,
            port=Config.APP_PORT,
            log_level="info"
        )

    except Exception as e:
        startup_logger.error(f"❌ 应用启动失败: {e}")
        raise

def cleanup_all_processes():
    """清理所有相关进程（8000端口和ARQ进程）"""
    startup_logger.info("🧹 开始全面清理相关进程...")

    # 清理8000端口
    cleanup_port_8000()

    # 清理ARQ进程
    cleanup_existing_workers()

    # 等待进程完全清理
    time.sleep(2)

    startup_logger.info("✅ 进程清理完成")

def check_port_and_kill(port: int = 8000):
    """检查端口占用并杀死进程"""
    try:
        import psutil
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                # 获取进程的网络连接
                connections = proc.connections()
                for conn in connections:
                    if hasattr(conn, 'laddr') and conn.laddr and conn.laddr.port == port:
                        startup_logger.warning(f"发现端口{port}被进程占用: PID={proc.info['pid']}, Name={proc.info['name']}")
                        proc.kill()
                        startup_logger.info(f"已杀死占用端口{port}的进程: PID={proc.info['pid']}")
                        time.sleep(1)  # 等待进程完全关闭
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                # 这些异常是正常的，进程可能已经结束或无权限访问
                startup_logger.debug(f"psutil进程访问异常（正常）: {type(e).__name__}: {e}")
    except ImportError as e:
        # 如果没有psutil，使用系统命令
        startup_logger.debug(f"psutil模块不可用，将使用系统命令: {e}")
    except Exception as e:
        startup_logger.warning(f"psutil检查端口失败: {e}，使用系统命令")

    # 使用系统命令检查端口
    try:
        result = subprocess.run(['lsof', '-ti', f':{port}'], capture_output=True, text=True)
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                if pid:
                    startup_logger.warning(f"发现端口{port}被进程占用: PID={pid}")
                    subprocess.run(['kill', '-9', pid])
                    startup_logger.info(f"已杀死占用端口{port}的进程: PID={pid}")
            time.sleep(1)
            return True
    except Exception as e:
        startup_logger.error(f"检查端口占用失败: {e}")

    return False

def main():
    """主函数"""
    startup_logger.info("=" * 60)
    startup_logger.info("🚀 AI SQL PCAP Analyzer 统一启动脚本")
    startup_logger.info("=" * 60)

    startup_logger.info("开始启动AI SQL PCAP Analyzer...")

    # 首先清理所有相关进程
    cleanup_all_processes()

    # 检查依赖
    startup_logger.info("检查系统依赖...")
    if not check_dependencies():
        startup_logger.error("❌ 依赖检查失败")
        return 1

    # 安装依赖
    startup_logger.info("检查Python依赖...")
    if not install_dependencies():
        startup_logger.error("❌ 依赖安装失败")
        return 1

    # 创建目录
    startup_logger.info("创建必要目录...")
    create_directories()

    # 记录环境信息
    startup_logger.info("记录环境信息...")
    log_environment_info()

    # 检查是否需要启动工作器
    start_with_worker = os.environ.get('START_WITH_WORKER', 'true').lower() == 'true'

    if start_with_worker:
        startup_logger.info("启动模式: API服务 + 后台工作器")

        # 创建进程 - 使用独立的Worker启动脚本
        app_process = Process(target=start_main_app)
        worker_process = None  # Worker将通过subprocess独立启动

        try:
            # 先启动主应用（API服务优先）
            startup_logger.info("🚀 优先启动API服务...")
            app_process.start()
            time.sleep(3)  # 等待API服务启动

            # 然后启动工作器（独立进程）
            startup_logger.info("🔄 启动独立Worker进程...")

            # 启动worker前再次确保清理
            startup_logger.info("🧹 启动Worker前最后一次清理...")
            cleanup_existing_workers()

            python_path = get_python_executable()

            # 创建Worker日志文件
            worker_log_dir = Path("logs/worker")
            worker_log_dir.mkdir(parents=True, exist_ok=True)
            worker_log_file = worker_log_dir / f"worker_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

            # 启动独立的ARQ Worker进程
            with open(worker_log_file, "w", encoding="utf-8") as log_file:
                log_file.write(f"=== ARQ Worker 启动于 {datetime.now()} ===\n")
                worker_process = subprocess.Popen([
                    python_path, "-m", "arq", "tasks.worker.WorkerSettings", "--verbose"
                ], stdout=log_file, stderr=subprocess.STDOUT, text=True, cwd=os.getcwd())

            startup_logger.info(f"✅ Worker进程已启动，PID: {worker_process.pid}")
            startup_logger.info(f"📝 Worker日志文件: {worker_log_file}")

            # 等待API进程结束
            app_process.join()

        except KeyboardInterrupt:
            startup_logger.info("⏹️ 收到中断信号，正在关闭...")

            # 终止API进程
            if app_process.is_alive():
                startup_logger.info("正在关闭API服务...")
                app_process.terminate()
                app_process.join(timeout=5)
                if app_process.is_alive():
                    app_process.kill()

            # 终止Worker进程
            if worker_process and worker_process.poll() is None:
                startup_logger.info("正在关闭Worker进程...")
                worker_process.terminate()
                try:
                    worker_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    worker_process.kill()
                    worker_process.wait()

            startup_logger.info("✅ 所有进程已关闭")

        except Exception as e:
            startup_logger.error(f"❌ 启动失败: {e}")
            return 1
    else:
        startup_logger.info("启动模式: 仅API服务")
        # 只启动主应用
        try:
            start_app_directly()
        except KeyboardInterrupt:
            startup_logger.info("⏹️ 服务被用户中断")
            return 0
        except Exception as e:
            startup_logger.error(f"❌ 启动失败: {e}")
            return 1

    return 0

def force_cleanup_only():
    """仅执行清理操作，不启动服务"""
    startup_logger.info("=" * 60)
    startup_logger.info("🧹 AI SQL PCAP Analyzer 强制清理模式")
    startup_logger.info("=" * 60)

    startup_logger.info("执行强制清理操作...")
    cleanup_all_processes()

    startup_logger.info("✅ 清理完成！")
    startup_logger.info("强制清理操作完成")

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--cleanup-only":
        try:
            force_cleanup_only()
            sys.exit(0)
        except Exception as e:
            startup_logger.error(f"💥 清理过程中发生错误: {e}")
            startup_logger.error(f"清理过程中发生错误: {e}")
            sys.exit(1)

    try:
        exit_code = main()
        startup_logger.info(f"程序退出，退出码: {exit_code}")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        startup_logger.info("⏹️ 服务被用户中断")
        startup_logger.info("服务被用户中断")
        sys.exit(0)
    except Exception as e:
        startup_logger.error(f"💥 启动过程中发生错误: {e}")
        startup_logger.error(f"启动过程中发生错误: {e}")
        sys.exit(1)
